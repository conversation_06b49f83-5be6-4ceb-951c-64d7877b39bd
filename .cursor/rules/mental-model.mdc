---
description: 
globs: 
alwaysApply: true
---
# FitRewards Platform: System Overview & Mental Model

## Section 1: Application Overview (Product Management Perspective)

**Purpose:**
The Fitness Rewards Platform, "FitRewards," is a comprehensive white-label gamification platform designed for fitness businesses (gyms, studios, corporate wellness programs). It motivates users through a sophisticated rewards system featuring points, tiers, milestones, and customizable rewards. The platform is multi-tenant, allowing different clients to have their own branded experience and custom configurations.

**User Personas:**

1.  **End User (Fitness Enthusiast):**

    - Primary consumer who interacts with the fitness rewards system.
    - Tracks fitness activities (e.g., class attendance) and progress.
    - Earns points, progresses through tiers, and unlocks milestone achievements.
    - Redeems points for rewards (e.g., free smoothies, merchandise).
    - Views a personalized dashboard with progress summaries.

2.  **Client Administrator (Gym/Studio Owner, HR Manager):**

    - Configures their organization's instance of the platform.
    - Sets up branding, defines custom milestones, and manages the rewards catalog.
    - Enables/disables platform features (e.g., tiers, leaderboards).
    - Monitors user engagement and progress.
    - Manages user roles within their organization.

3.  **Staff / Fulfiller (Gym Staff):**

    - Views a list of pending reward redemptions from users.
    - Marks redemptions as "Fulfilled" after providing the user with their reward.
    - Interacts with a specific admin-level interface for fulfillment tasks.

4.  **Platform Administrator (Internal):**
    - Manages multiple client configurations.
    - Handles platform-wide settings and operations.
    - Oversees data seeding and client onboarding.

**Core Features:**

- **User Management & RBAC:** Secure authentication via Clerk, with role-based access control (Admin, Staff, Member) synchronized from Clerk Organizations.
- **Gamification Engine:**
  - **Points System:** Core currency earned through activities with real-time updates.
  - **Tier Progression:** Five-tier system (Bronze → Silver → Gold → Platinum → Diamond) with automatic advancement.
  - **Configurable Milestone Engine:** Admins can define specific, repeatable achievements with custom point rewards and badge systems.
- **Multi-Tenant Architecture:** Client-specific data, branding, and feature configurations with complete isolation.
- **Activity Tracking:** Comprehensive logging of user activities with automated milestone evaluation.
- **Rewards Catalog & Fulfillment:** Full lifecycle management from catalog creation to staff fulfillment tracking.
- **Customizable Icons:** Admins can assign unique icons from the `huge-icons` library to all milestones, activities, and rewards for a fully branded experience.
- **Responsive Admin Interface:** Modern, mobile-first admin panel with role-based access controls.
- **Error Handling & Resilience:** Comprehensive error boundaries and graceful failure recovery.
- **Real-time Updates:** Live data synchronization via Convex with optimistic UI updates.
- **Webhook Integration:** Automated user profile and role synchronization with Clerk.
- **Input Validation:** Client-side and server-side validation with Zod schemas for data integrity.
- **User History Hub:** A dedicated section for users to view their complete activity history and a gallery of their unlocked achievements.

**High-Level User Flows:**

1.  **New User Onboarding:**

    - User signs up via Clerk on a client-branded page.
    - Clerk webhook triggers a `user.created` event.
    - Convex backend creates a `users` record. The user is assigned to a `client` and given a default role.
    - The user is redirected to their personalized dashboard.

2.  **Milestone Achievement Flow:**

    - User completes a fitness activity (e.g., attends a class).
    - The system logs this in the `activities` table.
    - A Convex function evaluates this new activity against all active `milestones` for that client.
    - If a milestone's conditions are met (e.g., `countThreshold` reached), the user is awarded points and a `userMilestoneProgress` record is created.
    - The user's dashboard updates in real-time to show new points and achieved milestone.

3.  **Reward Redemption & Fulfillment Flow:**

    - User browses the `RewardsPage` and spends points on a reward.
    - A `userRedemptions` record is created with `Pending` status.
    - The Staff/Fulfiller views a list of pending redemptions in the `RedemptionFulfillmentTab` on the admin page.
    - Staff provides the physical reward to the user (e.g., a smoothie).
    - Staff marks the redemption as `Fulfilled` in the UI.
    - The `userRedemptions` record is updated, and the user's history reflects the completed redemption.

4.  **Admin Configuration Flow:**

    - Client Admin accesses the `AdminPage`.
    - Navigates to the `MilestonesManagementTab` or `RewardsManagementTab`.
    - Creates/updates milestones or rewards using dedicated forms (`MilestoneFormModal`, `RewardFormModal`).
    - Changes are saved to the `milestones` and `rewards` tables and are immediately active for users of that client.

5.  **User History Viewing Flow:**
    - User navigates from the dashboard to the "History Hub".
    - User can toggle between a paginated "Activity History" feed and a "Achievements Gallery".
    - The system fetches the relevant data from `userMilestoneProgress` using dedicated Convex queries.

## Section 2: Software Architecture (Architect Perspective)

**Architectural Style:**
The application implements a **modern cloud-native architecture** using a **Monorepo** structure with **comprehensive testing infrastructure** and **robust error handling**. It leverages a **Backend-as-a-Service (BaaS)** platform (Convex) and a **decoupled Service Layer** for core business logic.

- **Monorepo Structure:** Managed by pnpm workspaces and Turbo for efficient, cohesive development with shared utilities and consistent tooling.
- **Frontend:** A resilient Single Page Application (SPA) built with React, Vite, and TypeScript featuring error boundaries and input validation.
- **Backend (BaaS):** Serverless architecture using Convex with structured logging, authentication helpers, and comprehensive error handling.
- **Service Layer (`packages/core`):** A fully tested, dedicated package containing pure business logic with 95%+ test coverage.
- **Authentication:** Fully managed by Clerk with centralized authentication helpers and role-based access control utilities.
- **Multi-Tenancy:** Achieved through database-level tenant isolation with client-specific configurations and admin interfaces.
- **Quality Assurance:** Comprehensive unit testing, input validation, structured logging, and error recovery mechanisms.

**Key Components/Modules:**

1.  **Frontend Application (`apps/web`):**

    - Modern React SPA with comprehensive error boundaries and input validation.
    - Features custom hooks for state management, reusable icon components (`HugeIcon.tsx`), and shared utilities.
    - Implements responsive admin interfaces with mobile-first design principles.
    - Integrates with Convex via `convex/react-clerk` and Clerk via `@clerk/clerk-react` with proper error handling.

2.  **Backend Infrastructure (`packages/db`):**

    - Robust Convex application with structured logging and authentication helpers.
    - `convex/schema.ts`: Comprehensive database schema with proper indexing and relationships.
    - `convex/functions/`: Serverless functions with centralized authentication and error handling.
    - `convex/lib/`: Shared utilities including `authHelpers.ts` and `logger.ts` for consistent patterns.
    - `convex/http.ts`: Webhook handlers with proper validation and error recovery.
    - `convex/auth.config.ts`: Authentication configuration with role-based access control.

3.  **Core Business Logic (`packages/core`):**

    - Fully tested, framework-agnostic package with Jest test suite and 95%+ coverage.
    - Contains pure TypeScript functions for `MilestoneService.ts`, `TierService.ts`, and other domain logic.
    - Includes comprehensive unit tests in `__tests__/` directories with edge case coverage.
    - Designed for portability, reusability, and independent testing.

4.  **Shared Libraries & Assets:**
    - `apps/web/src/utils/`: Common utility functions for formatting, validation, and data manipulation.
    - `apps/web/src/lib/`: Validation schemas, error handling utilities, and shared configurations.
    - `clients/`: Tenant-specific configurations, assets, and documentation with style guides.

**Data Models & Relationships:**
The database schema is defined in `packages/db/convex/schema.ts` and is significantly more detailed than a simple user/client model. Key tables include:

- `users`: Stores user profiles, linked to a `clerkUserId` and a `clientId`. Includes points, tier, and role.
- `clients`: Represents the tenant organizations.
- `clientConfiguration`: Holds client-specific branding, feature flags, and dashboard layouts.
- `activities`: A log of all significant user actions (e.g., `class_attendance`). This is the event source for the gamification engine.
- `activityTypes`: Defines the types of activities that can be logged (e.g., "Class Attendance"), each with a required `iconUrl`.
- `milestones`: Configurable rules that trigger rewards based on activity patterns (e.g., attend 10 classes), each with a required `iconUrl`.
- `rewards`: The catalog of items users can purchase with points, each with a required `imageUrl` for its icon.
- `userRedemptions`: A join table tracking which users have redeemed which rewards, including a `status` for the fulfillment workflow.
- `userMilestoneProgress`: Tracks which milestones a user has achieved, storing a denormalized `milestoneIconUrl` for efficient display.
- `tierProgressions`: Logs the history of a user's advancement through tiers.

**Diagrams (Mermaid.js):**

**System Architecture Overview:**

```mermaid
graph TD
    subgraph "User Layer"
        User[End Users]
        Admin[Client Admins & Staff]
    end

    subgraph "Frontend (Vercel)"
        WebApp[React SPA<br/>apps/web]
        ErrorBoundaries[Error Boundaries<br/>Resilient UI Components]
        ValidationSchemas[Input Validation<br/>Zod Schemas]
        CustomHooks[Custom Hooks<br/>State Management]
        SharedUtils[Shared Utilities<br/>Common Functions]

        WebApp --> ErrorBoundaries
        WebApp --> ValidationSchemas
        WebApp --> CustomHooks
        WebApp --> SharedUtils
        WebApp --> ClerkReact[Clerk React SDK]
        WebApp --> ConvexReact[Convex React SDK]
    end

    subgraph "Authentication (Clerk Cloud)"
        ClerkAuth[Clerk Service]
        ClerkAuth --> Webhooks[User & Org Webhooks]
    end

    subgraph "Backend (Convex Cloud)"
        ConvexAPI[Convex API Layer]
        ConvexDB[(Convex Database)]
        subgraph "Convex Runtime"
            Functions[Serverless Functions<br/>packages/db/convex/functions]
            WebhookHandler[HTTP Action<br/>packages/db/convex/http.ts]
            AuthHelpers[Authentication Helpers<br/>packages/db/convex/lib/authHelpers.ts]
            Logger[Structured Logging<br/>packages/db/convex/lib/logger.ts]
            CoreServices[Core Service Logic<br/>packages/core]
            TestSuite[Unit Tests<br/>packages/core/src/services/__tests__]
        end

        ConvexAPI --> Functions
        WebhookHandler --> Functions
        Functions --> AuthHelpers
        Functions --> Logger
        Functions --> CoreServices
        Functions --> ConvexDB
        CoreServices --> TestSuite
    end

    User --> WebApp
    Admin --> WebApp
    ClerkReact --> ClerkAuth
    ConvexReact --> ConvexAPI
    Webhooks --> WebhookHandler
```

**User Authentication & Role Synchronization Flow:**

```mermaid
sequenceDiagram
    participant User
    participant Frontend as React SPA
    participant Clerk
    participant Webhook as Convex HTTP Handler
    participant Functions as Convex Functions

    User->>Frontend: Sign In / Sign Up
    Frontend->>Clerk: Authenticate User
    Clerk-->>Frontend: Return JWT

    alt User Creation/Update
        Clerk->>Webhook: POST /webhooks/clerk (user.created)
        Webhook->>Functions: internalStoreOrUpdateUser
    else Role Update
        Clerk->>Webhook: POST /webhooks/clerk (organizationMembership.created)
        Webhook->>Functions: internalSetUserRole
    end

    Frontend->>Functions: API Call with JWT
    Functions->>ConvexDB: Query Data
    ConvexDB-->>Functions: Return Data
    Functions-->>Frontend: Return Data to UI
```

**Data Model Relationships:**

```mermaid
graph TD
    Users -- "Belongs to" --> Clients
    ClientConfiguration -- "Configures" --> Clients
    Activities -- "Performed by" --> Users
    Activities -- "Belongs to" --> Clients
    Milestones -- "Configured for" --> Clients
    Rewards -- "Offered by" --> Clients
    UserRedemptions -- "Made by" --> Users
    UserRedemptions -- "Fulfills" --> Rewards
    UserMilestoneProgress -- "Tracks for" --> Users
    UserMilestoneProgress -- "Corresponds to" --> Milestones
    TierProgressions -- "Logs for" --> Users
```

## Section 3: Development Details (Developer Perspective)

**Technology Stack:**

- **Core:** TypeScript, Node.js with strict type checking and comprehensive error handling
- **Monorepo:** pnpm workspaces, Turbo (`^1.13.3`) with optimized build pipelines
- **Frontend:** React (`^18.3.1`), Vite (`^5.2.11`), React Router (`^6.23.1`), `huge-icons`
- **Styling:** Tailwind CSS (`^3.4.3`) with PostCSS/Autoprefixer and responsive design patterns
- **Backend:** Convex (`^1.24.8`) with structured logging and authentication helpers
- **Authentication:** Clerk (`^5.0.0`) with centralized role-based access control
- **Webhook Verification:** Svix (`^1.22.0`) for secure webhook processing
- **Data Validation:** Zod (`^3.23.8`) for client-side and server-side input validation
- **Testing:** Jest (`^29.7.0`) with ts-jest for comprehensive unit testing
- **Code Quality:** ESLint, Prettier, TypeScript (strict) with automated formatting and linting

**Project Structure Deep Dive:**

```
fitness-rewards-platform/
├── 📁 apps/
│   └── 📁 web/                    # Main React frontend SPA
│       ├── 📄 vite.config.ts      # Build configuration
│       └── 📁 src/
│           ├── 📄 main.tsx         # App entry point with error boundaries
│           ├── 📄 AppRoutes.tsx    # Routes with error boundary protection
│           ├── 📁 pages/           # Top-level page components
│           │   └── 📁 history/     # User history pages
│           ├── 📁 components/      # Reusable UI components
│           │   ├── 📁 admin/       # Admin interface components
│           │   ├── 📁 icons/       # Reusable icon components (HugeIcon.tsx)
│           │   ├── 📁 history/     # Components for the history hub
│           │   └── ErrorBoundary.tsx # Error handling components
│           ├── 📁 hooks/           # Custom React hooks
│           ├── 📁 utils/           # Shared utility functions
│           └── 📁 lib/             # Validation schemas and configurations
├── 📁 packages/
│   ├── 📁 core/                   # Fully tested business logic
│   │   ├── 📄 jest.config.js      # Jest testing configuration
│   │   └── 📁 src/services/
│   │       ├── 📁 __tests__/      # Comprehensive unit tests
│   │       ├── MilestoneService.ts # Milestone evaluation logic
│   │       └── TierService.ts      # Tier progression logic
│   ├── 📁 db/                     # Convex backend application
│   │   └── 📁 convex/
│   │       ├── 📄 schema.ts       # Database schema and relationships
│   │       ├── 📄 http.ts         # Webhook handlers with validation
│   │       ├── 📄 auth.config.ts  # Authentication configuration
│   │       ├── 📁 lib/            # Shared backend utilities
│   │       │   ├── authHelpers.ts # Authentication helper functions
│   │       │   └── logger.ts      # Structured logging system
│   │       └── 📁 functions/      # Serverless functions with error handling
│   │           └── history.ts     # Queries for user history
│   ├── 📁 shared/                 # (Future) Shared types and utilities
│   └── 📁 emails/                 # (Future) Email service integration
├── 📁 clients/                    # Tenant-specific assets and configuration
│   └── 📁 The Handle Bar/
│       └── 📁 docs/
│           └── THE_HANDLE_BAR_STYLE_GUIDE.md
└── 📄 turbo.json              # Monorepo build pipeline configuration
```

**Key Development Patterns:**

1.  **Backend-as-a-Service (BaaS) Integration:** The application leverages Convex for all backend infrastructure with structured logging, authentication helpers, and comprehensive error handling built on top.

2.  **Decoupled Business Logic (Service Layer):** Core domain logic is isolated in `packages/core` with comprehensive unit testing (95%+ coverage). This separation enables independent testing, reusability, and maintainability.

3.  **Error-First Design:** Every component and function is designed with error handling in mind, featuring error boundaries, input validation, graceful degradation, and structured logging.

4.  **Webhook-Driven User Sync:** Event-driven architecture using Clerk webhooks with proper validation, error recovery, and audit logging for user synchronization.

5.  **Component Composition:** Large components are broken down into smaller, focused components with custom hooks for state management and reusable utilities.

6.  **Input Validation:** Comprehensive client-side and server-side validation using Zod schemas to ensure data integrity and prevent runtime errors.

7.  **Testing-First Development:** All business logic includes comprehensive unit tests with edge case coverage, establishing patterns for future development.

8.  **Configuration-Driven UI:** Multi-tenant features controlled by database configuration with responsive admin interfaces for real-time customization.

**Authentication & Authorization:**

- **Authentication:** Handled by Clerk. JWTs issued by Clerk are passed with every Convex API call. Convex automatically validates these tokens on the backend.
- **Authorization (RBAC):** A user's role (`admin` or `member`) is synced from their Clerk Organization membership via webhook into the `users.role` field. Convex functions and frontend components can then check this role to authorize access to specific data or functionality (e.g., the entire `/admin` page).

**Build Process & DevOps:**

- **Monorepo Tooling:** `turbo` orchestrates build, dev, test, and linting processes with optimized caching and parallel execution.
- **Development:** `pnpm dev` starts all services in parallel with hot reloading and real-time error reporting.
- **Dependencies:** Managed with `pnpm workspaces` with shared dependencies and consistent versioning.
- **Testing Infrastructure:** Comprehensive Jest setup with TypeScript support, coverage reporting, and automated test execution.
- **Quality Assurance:** Automated linting, formatting, type checking, and test execution in the build pipeline.
- **Deployment:** Frontend deployed on Vercel with error monitoring, backend on Convex cloud with structured logging.
- **Environment Management:** Secure environment variable handling across development, staging, and production environments.

## Section 4: Quality Assurance & Recent Improvements

**Testing Infrastructure:**

The platform now includes comprehensive testing infrastructure with a focus on reliability and maintainability:

- **Unit Testing:** Full Jest test suite for `packages/core` with 95%+ coverage including edge cases and error scenarios.
- **Test Organization:** Structured `__tests__/` directories with descriptive test names and comprehensive assertions.
- **Continuous Testing:** Automated test execution in the build pipeline with coverage reporting and failure notifications.

**Error Handling & Resilience:**

- **Error Boundaries:** React error boundaries at route and component levels prevent application crashes and provide graceful degradation.
- **Input Validation:** Comprehensive Zod schemas for all user inputs with client-side and server-side validation.
- **Structured Logging:** Centralized logging system with context, performance timing, and environment-aware output formatting.
- **Authentication Helpers:** Centralized authentication utilities with consistent error handling and role-based access control.

**Code Organization & Maintainability:**

- **Shared Utilities:** Common functions extracted into reusable utilities reducing code duplication and improving consistency.
- **Component Refactoring:** Large components broken down into focused, single-responsibility components with custom hooks.
- **Icon Components:** Reusable icon library with consistent styling and accessibility features.
- **Admin Layout:** Shared admin interface components with responsive design and mobile-first approach.

**Development Experience:**

- **Type Safety:** Comprehensive TypeScript usage with strict configuration and proper type definitions.
- **Code Quality:** Automated linting, formatting, and type checking with pre-commit hooks.
- **Documentation:** Comprehensive JSDoc documentation for all functions and components.
- **Performance:** Optimized build pipeline with caching, tree shaking, and code splitting capabilities.

**Security & Validation:**

- **Input Sanitization:** All user inputs validated and sanitized using Zod schemas with proper error messages.
- **Role-Based Access:** Centralized authentication helpers ensuring consistent permission checking.
- **Error Information:** Secure error handling that doesn't expose sensitive information to end users.
- **Webhook Security:** Proper webhook validation and signature verification for external integrations.
