#!/bin/bash
# A script to fully clean and reinstall all dependencies in the monorepo.

# Exit immediately if a command exits with a non-zero status.
set -e

echo "🧹 Starting a full reinstall of the workspace..."

# Navigate to the project root directory, which is one level up from the script's directory.
cd "$(dirname "$0")/.."

echo "🔥 Removing all node_modules directories..."
# Find all directories named 'node_modules' and remove them forcefully and recursively.
# The '-prune' option prevents 'find' from descending into found directories, which is more efficient.
find . -name "node_modules" -type d -prune -exec rm -rf '{}' +

echo "🔥 Removing lock files to ensure a fresh install..."
# Remove lock files from various package managers to avoid conflicts.
rm -f pnpm-lock.yaml
rm -f package-lock.json
rm -f yarn.lock

echo "🧹 Pruning the pnpm store to remove orphaned packages..."
# It's good practice to occasionally prune the store to free up disk space.
if command -v pnpm &> /dev/null
then
    pnpm store prune
else
    echo "pnpm command not found. Skipping 'pnpm store prune'."
fi


echo "✅ Workspace cleaned."

echo "📦 Installing dependencies using pnpm..."
# Install all dependencies using pnpm. This will create a new pnpm-lock.yaml.
if command -v pnpm &> /dev/null
then
    pnpm install
else
    echo "pnpm command not found. Please install pnpm and run 'pnpm install' manually."
    exit 1
fi

echo "🎉 Reinstall complete! Your workspace is fresh and ready to go." 