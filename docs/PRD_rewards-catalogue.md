# PRD: Rewards Catalogue Management

**Author:** <PERSON> (Product Manager)
**Version:** 1.0
**Status:** In Review

---

## 1. Introduction

### 1.1. Overview

This document outlines the requirements for the "Rewards Catalogue Management" feature. Currently, the rewards available to members are static and require developer intervention to change. To empower our clients and make the platform truly self-service, we need to provide a user-friendly interface for Client Administrators to create, edit, and manage their own unique rewards catalogue.

This feature will introduce a new "Rewards Management" section within the admin panel, allowing admins to dynamically control the incentives offered to their members.

### 1.2. Product Goals & Strategy

This feature is critical to our platform's core value proposition of offering a customizable, white-label rewards system. By enabling self-service rewards management, we will:

- **Increase Client Autonomy:** Empower clients to tailor their rewards program to their specific business goals, events, and member demographics without needing developer support.
- **Enhance Member Engagement:** Allow clients to keep their rewards fresh and exciting, encouraging sustained member participation.
- **Streamline Operations:** Reduce the operational overhead for both our team and the client's team by removing the need for manual reward updates.

---

## 2. Target Audience

The primary user of this feature is the Client Administrator, who is responsible for the overall strategy and configuration of their organization's rewards program.

- **Primary Role:** `Admin`
- **User Persona: <PERSON>, the Studio Owner**
  - **Who he is:** <PERSON> owns two "The Handle Bar" studios. He is business-savvy and always looking for ways to increase member retention and secondary revenue (like merchandise sales).
  - **His Goals & Pains:** David wants to run special promotions, like a "Summer Challenge," with unique, limited-time rewards. He finds it frustrating that he has to submit a request and wait for a developer to add or change rewards, which slows down his marketing efforts. He wants direct control over the rewards his members see.
  - **His Needs:** David needs a simple, visual interface where he can easily add new rewards (e.g., a "Limited Edition Summer Tank Top"), set their point cost, write a compelling description, and activate or deactivate them at will.

---

## 3. User Stories

### Epic: Admin Manages Rewards Catalogue

| ID  | User Story                                                                                                                                         | Role  | Priority  |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------------- | ----- | --------- |
| 1   | As an admin, I want to see a clear, organized list of all rewards for my studio, including their name, cost, and current status (active/inactive). | Admin | Must-Have |
| 2   | As an admin, I want a simple way to create a new reward, providing its name, point cost, description, and an icon/image.                           | Admin | Must-Have |
| 3   | As an admin, I want to edit all the details of an existing reward.                                                                                 | Admin | Must-Have |
| 4   | As an admin, I want to quickly activate or deactivate a reward to control its visibility in the member-facing catalogue.                           | Admin | High      |
| 5   | As an admin, I want the system to validate my inputs to ensure I don't create a reward with no name or a negative point cost.                      | Admin | Must-Have |
| 6   | As an admin, I need the interface to be intuitive and require no special training to use.                                                          | Admin | Must-Have |

---

## 4. Functional Requirements

### 4.1. UI & Access

- A "Rewards Management" tab shall be available in the main navigation of the admin panel, visible only to users with the `Admin` role.
- This view will display a list or table of all rewards configured for the client's organization.
- Each reward in the list must display:
  - Reward Name
  - Point Cost
  - Status (e.g., a toggle showing "Active" or "Inactive")
  - Action buttons ("Edit", "Delete" - with confirmation).

### 4.2. Create/Edit Workflow

1.  A prominent "Create New Reward" button will open a form modal (`RewardFormModal`).
2.  Clicking the "Edit" button on an existing reward will open the same modal, pre-populated with that reward's data.
3.  The form shall contain the following fields:
    - **`name`**: Text input. Required, must not be empty.
    - **`description`**: Text area. Optional.
    - **`cost`**: Number input. Required, must be a positive integer.
    - **`imageUrl`**: Text input for icon/image URL. For this implementation, we will use Huge Icons names (e.g., `beverage-02`, `t-shirt`).
    - **`isActive`**: A boolean toggle switch, defaulted to `true` (Active) for new rewards.
4.  The modal will have "Save" and "Cancel" actions.
    - On "Save," the frontend will call a mutation (`createReward` or `updateReward`) with the validated form data.
    - The system will provide clear loading/success/error feedback (e.g., toasts).
    - Upon success, the modal will close and the list of rewards will update in real-time.

### 4.3. Activation Workflow

- Each reward in the list will have an `isActive` toggle switch.
- Toggling this switch will immediately call a mutation (`updateRewardStatus`) to change the reward's `isActive` status on the backend.
- This allows an admin to quickly hide or show a reward without a full edit cycle.

---

## 5. Rewards Catalogue (Sample for "The Handle Bar")

This list provides a sample catalogue with a range of rewards designed to motivate members at different levels of engagement.

| Reward Name                             | Point Cost | Description                                                                  | Huge Icon Placeholder Name |
| --------------------------------------- | ---------- | ---------------------------------------------------------------------------- | -------------------------- |
| **Choice of Post-Workout Drink**        | 160        | Refuel after your ride! Your choice of a free smoothie or kombucha on tap.   | `beverage-02`              |
| **Free Towel Service for a Day**        | 100        | Forget your towel? We've got you covered. Enjoy complimentary towel service. | `towel`                    |
| **Branded Water Bottle**                | 350        | Stay hydrated in style with our signature Handle Bar water bottle.           | `bottle`                   |
| **Bring a Friend for Free**             | 750        | Share the sweat! Bring a friend to any class on us.                          | `user-add-01`              |
| **$10 Off Merchandise**                 | 1,000      | Treat yourself. Get $10 off any item from our merchandise collection.        | `ticket-01`                |
| **Early Access to Class Booking**       | 1,500      | Book your favorite bike, worry-free. Get 24-hour early access to booking.    | `calendar-check-02`        |
| **30-Min Personal Form Assessment**     | 2,500      | Perfect your form in a 1-on-1 session with one of our expert instructors.    | `activity`                 |
| **The Handle Bar Signature Sweatshirt** | 4,000      | The ultimate cozy reward. Our super-soft, signature branded sweatshirt.      | `t-shirt-01`               |
| **Free Entry to a 2-Hour Workshop**     | 5,000      | Deepen your practice. Gain free entry to any upcoming special workshop.      | `award-04`                 |
| **One Month of Unlimited Classes**      | 10,000     | The ultimate prize! A full month of unlimited classes on us.                 | `trophy-01`                |

---

## 6. Non-Functional Requirements

| Category           | Requirement                                                                                                                                   |
| ------------------ | --------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability**      | The interface must be extremely intuitive. An admin should be able to manage the entire catalogue without needing documentation.              |
| **Performance**    | Loading the rewards list and submitting the form should feel instantaneous. Optimistic updates should be used to reflect changes immediately. |
| **Security**       | Access must be strictly role-gated on both the frontend (UI hidden) and backend (mutations protected) to `Admin` users only.                  |
| **Data Integrity** | Server-side validation must be enforced to ensure that all rewards have a valid name and cost.                                                |

---

## 7. Out of Scope

- Management of reward inventory (e.g., "only 10 tank tops available").
- Scheduling rewards to become active/inactive automatically at a future date.
- Sorting or re-ordering of rewards in the admin view.

---

## 8. Success Metrics

| Metric                          | How We'll Measure It                                                         | Target                                           |
| ------------------------------- | ---------------------------------------------------------------------------- | ------------------------------------------------ |
| **Time to Create a New Reward** | Measure the average time from clicking "Create" to successful save.          | < 60 seconds on average.                         |
| **Admin Autonomy**              | Track the number of support tickets related to "add/change reward" requests. | 95% reduction in reward-related support tickets. |
| **Client Satisfaction**         | Survey clients 1 month post-launch on the ease of use of the new feature.    | Average rating of 4.5/5 or higher.               |
