# PRD: Automated Activity Integration

**Author:** <PERSON> (Product Manager)
**Version:** 2.0
**Status:** In Review

---

## 1. Introduction

### 1.1. Overview

This document outlines the requirements for the **Automated Activity Integration** feature. This is a critical, foundational feature that transitions the platform's core data source from manual, user-driven input to a fully automated, reliable pipeline connected to third-party fitness management systems like Mariana-Tek and MindBody.

Instead of requiring users to remember to "Log Activity," this feature will create a seamless, "magical" experience where members are automatically rewarded for verified class check-ins, directly from the system their studio already uses.

### 1.2. Product Goals & Strategic Importance

This feature is the most critical step in achieving a true Minimum Viable Product (MVP) for our B2B clients.

- **Achieve Product-Market Fit:** Transition the platform from a "nice-to-have" novelty into an essential, integrated operational tool for fitness studios. Automation is the core value proposition for our target market.
- **Ensure Data Integrity:** By using the studio's booking software as the source of truth, we eliminate the possibility of fraudulent manual entries and ensure the rewards economy is built on verified actions.
- **Create a Frictionless User Experience:** Automating activity logging removes all cognitive load from the end-user, creating a delightful experience where rewards appear "automatically" for their regular behavior.
- **Drive Scalability:** Establish a repeatable pattern for integrating with various third-party systems, enabling the platform to expand its addressable market.

---

## 2. Target Audience

### 2.1. Primary Persona: "David" - The Hands-Off Studio Owner (Client Administrator)

- **Role:** Owner of "The Handle Bar," a multi-location boutique fitness studio. David is focused on high-level business strategy, growth, and maintaining a premium brand experience.
- **Goals & Motivations:**
  - "I need tools that enhance my member experience without adding to my staff's workload."
  - "I want our rewards program to feel like a natural extension of our brand, not a separate app members have to think about."
  - "The data must be accurate. I need to trust that points are awarded only for actual, verified check-ins."
- **Frustrations:**
  - He worries about "yet another system" for his staff to manage.
  - Manual processes are prone to human error and don't scale as he opens new locations.
  - He feels that asking members to manually log their own attendance cheapens the premium, seamless experience he wants to cultivate.
- **How this feature helps:** David gets a "set it and forget it" system. Once connected, the rewards platform works silently in the background, reinforcing his brand's value proposition without requiring any ongoing effort from his team or his members.

### 2.2. Secondary Persona: "Sarah" - The Seamlessly Rewarded Member (End User)

- **Role:** A loyal member of "The Handle Bar" who attends 3-4 classes per week.
- **Goals & Motivations:**
  - "I love feeling recognized for my loyalty, but I'm busy. I don't want to do 'homework' for a rewards program."
  - "It should just work. When I show up for class, the system should know."
  - She is motivated by seeing her points grow and saving up for a piece of studio merchandise.
- **Frustrations:**
  - She often forgets to log her activity in other fitness apps and would likely do the same here.
  - If she had to manually log an activity and the system failed, she would lose trust in the program.
- **How this feature helps:** Sarah's experience is entirely frictionless. She continues her normal routine of booking and checking into classes. The rewards platform delights her by proactively notifying her of points earned and milestones achieved, making her feel valued without requiring any effort on her part.

---

## 3. User Stories

### Epic: Automate Activity Logging via Third-Party Integration

| ID        | User Story                                                                                                                                                                         | Role         | Priority  |
| :-------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------- | :-------- |
| **INT-1** | As a Client Admin, I want a secure, one-time process to connect my studio's Mariana-Tek (or MindBody) account to the rewards platform.                                             | Client Admin | Must-Have |
| **INT-2** | As a Client Admin, I want to map specific class types from my booking system (e.g., "Cycling 45") to a generic `Activity Type` in the rewards platform (e.g., "Class Attendance"). | Client Admin | Must-Have |
| **INT-3** | As an End User, when I am checked into a class by studio staff, I want my attendance to be automatically logged in the rewards app so that I earn points seamlessly.               | End User     | Must-Have |
| **INT-4** | As a Client Admin, I want to see the status of the API connection (e.g., "Connected," "Error") and receive a notification if the integration fails.                                | Client Admin | Must-Have |
| **INT-5** | As a developer, I need a secure and reliable way to receive and process webhook events from third-party systems to ensure no check-ins are missed.                                 | Developer    | Must-Have |
| **INT-6** | As a developer, I need robust, structured logging for the entire webhook lifecycle so I can easily trace and debug a single check-in event from receipt to completion.             | Developer    | Must-Have |
| **INT-7** | As a system, I must securely store encrypted API credentials and use them to validate incoming webhooks to prevent unauthorized access.                                            | System       | Must-Have |
| **INT-8** | As a system, I must handle webhook processing failures gracefully, using a retry mechanism to ensure eventual data consistency without losing check-in events.                     | System       | High      |

---

## 4. Functional Requirements

### 4.1. Database Schema Changes

1.  **Update `users` Table:** Add a new, indexed field to link users to their profiles in external systems. This is crucial for performance.
    ```typescript
    // packages/db/convex/schema.ts
    // ... in users table definition
    externalIds: v.optional(v.array(v.object({
      provider: v.string(), // e.g., "marianaTek", "mindBody"
      userId: v.string(), // The user's ID in the external system
    }))),
    // Add a search index for efficient lookups
    }.index('by_external_id', ['externalIds.provider', 'externalIds.userId'])
    ```
2.  **New Table: `clientApiCredentials`:** A secure table to store encrypted API credentials and webhook secrets for each client's integrations.
    ```typescript
    // packages/db/convex/schema.ts
    clientApiCredentials: defineTable({
      clientId: v.id('clients'),
      provider: v.string(), // "marianaTek", "mindBody"
      encryptedApiKey: v.string(), // API key encrypted at rest
      encryptedWebhookSecret: v.string(), // Webhook signing secret encrypted at rest
    }).index('by_client_and_provider', ['clientId', 'provider']),
    ```

### 4.2. Admin Dashboard UI (`apps/web/`)

1.  **New Settings Tab:** Add a new "Integrations" tab to the `AdminPage`.
2.  **Integration Management UI:**
    - This view will list available integrations (e.g., "Mariana-Tek," "MindBody").
    - Each integration will show a status badge ("Not Connected," "Connected," "Error") and a "Configure" button.
3.  **Configuration Modal:**
    - **API Credentials:** Secure input fields for the required API key and webhook signing secret. A "Test Connection" button will trigger a backend action to validate the credentials against the third-party API.
    - **Activity Mapping (MVP):** For the initial version, this will be a simple, single mapping. A dropdown will show all available `Activity Types` (e.g., "Class Attendance"), allowing the admin to map all incoming check-ins to that one activity.
    - **Enable/Disable Toggle:** A master toggle to activate or deactivate the integration for the client.

### 4.3. Backend Logic (`packages/db/convex/`)

1.  **New `integrations.ts` file:**

    - **`saveApiCredentials`:** An `internalMutation` that encrypts and saves credentials. Requires admin role.
    - **`testApiConnection`:** An `internalAction` that uses stored credentials to make a test call.

2.  **Update `http.ts` (Webhook Handler):**

    - **New Endpoint:** A new generic `httpAction` at `POST /webhooks/integrations/:provider`.
    - **Security First:** The handler's FIRST step MUST be to verify the webhook's signature using the client's stored secret. If verification fails, it must immediately return a `401 Unauthorized` error.
    - **Asynchronous Processing:** Upon successful verification, the handler immediately schedules an `internalAction` to process the payload and returns a `200 OK` response. This entire process must take <1s.

3.  **New `processWebhook.ts` Action:**
    - This `internalAction` contains the core business logic.
    1.  **Find Client:** Identify the `clientId` from the request.
    2.  **Find User:**
        - Extract the external user ID from the payload.
        - **Primary Lookup:** Query the `users` table using the new `by_external_id` index.
        - **Fallback Lookup:** If no match is found, query by the user's email from the payload.
        - **Link User:** If a user is found via the fallback email search, permanently add the `externalId` to their user record for efficient lookups next time.
        - **Log Unmatched:** If no user can be found, log the event to an "unmatched_events" table for future administrative review.
    3.  **Transform & Execute:** Convert the payload into the arguments for the existing `logActivity` mutation and call it. The entire gamification pipeline (points, milestones, tiers) is triggered from this point.

---

## 5. Non-Functional Requirements

| Category          | Requirement                                                                                                                                                                                                       |
| :---------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability**     | The admin configuration UI must be simple and self-explanatory, allowing a non-technical user to set up the integration in under 5 minutes.                                                                       |
| **Security**      | API credentials and secrets MUST be encrypted at rest. Webhook endpoints MUST be secured with signature verification. The system must be protected against replay attacks by validating webhook timestamps.       |
| **Reliability**   | The webhook processing system must be robust. Implement an automatic retry mechanism with exponential backoff (e.g., via Convex cron jobs or scheduled actions) for failed processing attempts.                   |
| **Performance**   | Webhook endpoints must respond in **<1 second**. Asynchronous processing is mandatory. User lookups must be highly optimized using database indexes (`by_external_id`).                                           |
| **Observability** | Implement structured logging for the entire webhook-to-activity flow. Each log entry should contain a unique `traceId` allowing developers to filter and trace a single check-in event through the entire system. |

---

## 6. Out of Scope

- **Historical Data Import:** V1 will not support backfilling a member's activity history from before the integration was enabled.
- **Bi-Directional Sync:** This is a one-way sync _into_ our platform. We will not be writing any data back to the third-party systems.
- **Complex Activity Mapping UI:** The initial version will only support mapping all check-ins to a single activity type. A more advanced UI for mapping different class types to different activities is out of scope.
- **Admin UI for Unmatched Events:** While we will log unmatched check-ins, a UI for admins to manually review and resolve them is out of scope for V1.

---

## 7. Success Metrics

| Metric                         | How We'll Measure It                                                                                                                                                   | Target                                                                  |
| :----------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------- |
| **Integration Adoption Rate**  | We will run a daily query to calculate `(Number of clients with active API credentials / Total number of active clients) * 100`.                                       | **80%** of clients connected within 60 days of launch.                  |
| **Automation Rate**            | We will add a `source` field to every record in the `activities` table. We will measure the percentage of new activities where `source` is `"webhook"` vs. `"manual"`. | **>99%** of all activities logged via automation for connected clients. |
| **Webhook Error Rate**         | We will track failures in our webhook processing action. The metric is `(Number of failed processing actions / Total incoming webhooks) * 100`.                        | **< 0.5%**.                                                             |
| **User Matching Success Rate** | We will measure this by `(Number of webhooks successfully matched to a user / Total number of processed webhooks) * 100`.                                              | **> 98%** (acknowledging that data mismatches can occur).               |

---

## 8. Open Questions / Assumptions

- **Assumption:** The primary user identifier for matching across systems will be the user's email address. We assume this is unique and consistently maintained in the client's systems.
- **Question (Technical Discovery):** What are the precise authentication methods, webhook payload structures, and signature verification algorithms for Mariana-Tek and MindBody?
- **Question (Technical Discovery):** What are the API rate limits for our primary integration targets? This is critical for the "Test Connection" feature and informs our error handling strategy.
- **Question (Business Logic):** If a studio changes its booking system, what is the required process for migrating their integration settings and user links?
- **Assumption (MVP Scope):** For the initial launch, mapping all incoming check-ins to a single, client-configurable `Activity Type` (e.g., "Class Attendance") is sufficient to provide value. Granular mapping is a fast-follow feature.
- **Question (Reliability):** What is the desired behavior for repeated, unmatchable check-in events for the same external user? Do we stop logging them after a certain threshold to reduce noise?
