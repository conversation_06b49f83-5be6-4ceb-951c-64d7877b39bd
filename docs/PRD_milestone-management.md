# Product Requirements Document: Client-Facing Milestone Management

**Document Version:** 1.1
**Author:** Product Management Team
**Status:** Approved for Development

---

## 1. Introduction

### 1.1. Feature Overview & Strategic Alignment

The **Client-Facing Milestone Management** feature is the next evolution of our client empowerment strategy, building directly on the success of the self-service Rewards Management system. This feature will provide Client Administrators with a dedicated UI within the Admin Dashboard to independently create, configure, update, and manage the complete lifecycle of their member-facing milestones.

Milestones are the narrative engine of our gamification platform; they transform routine activities into meaningful achievements, drive consistent member engagement, and give our clients a powerful tool to shape their community's experience. Currently, this powerful tool is locked behind a manual process requiring engineering support, creating a significant bottleneck.

This feature directly aligns with our core product strategy to **increase client autonomy and reduce operational dependency** by:

- **Empowering Real-Time Engagement:** Allowing clients to instantly launch dynamic and timely challenges (e.g., "New Year's Resolution Kickstart," "Summer Wellness Challenge") without development lead times.
- **Deepening Client Investment:** Providing clients with sophisticated tools to build unique, brand-aligned engagement loops that increase the platform's value and stickiness.
- **Driving Operational Efficiency:** Drastically reducing the high-touch support and engineering effort currently required for milestone configuration, freeing up internal resources for new feature development.
- **Completing the Self-Service Toolkit:** Creating a cohesive admin experience where both rewards and milestones are managed through a single, intuitive interface.

### 1.2. Problem Statement

**Current State:** All milestone catalog changes (adding, editing, or deactivating) require a detailed specification to be sent to our support team, who then must execute manual database updates. This process is slow, inefficient, prone to human error, and prevents clients from being agile.

**Client Pain Points:**

- "I have a great idea for a weekend challenge, but by the time I can get the milestone built, the opportunity has passed."
- "I want to tweak the point reward for a milestone to see if it improves engagement, but the process is too cumbersome to experiment."
- "My members are asking about the next big challenge, and I can't give them a firm answer because I'm waiting for it to be implemented."

**Business Impact:** High operational costs due to manual engineering work, a frustrating client experience with long lead times for changes, and missed engagement opportunities for our clients and their members.

### 1.3. Vision for Success

**Target State:** Client administrators can log in to a secure admin dashboard, navigate to a "Milestone Management" tab, and have full, instantaneous control over their milestone offerings. This seamless workflow allows clients to be more creative, responsive, and data-driven with their engagement strategies, transforming milestones from a static feature into a dynamic tool for community building.

---

## 2. Target Audience

### 2.1. Primary Persona: "David" - The Empowered Studio Owner (Client Administrator)

- **Role:** Owner of "The Handle Bar," a boutique fitness studio. He is a savvy business operator focused on both member experience and revenue growth.
- **Goals & Motivations:**
  - "I want to create unique, branded challenges that my members can't get anywhere else."
  - "I need to react quickly to member feedback and seasonal trends with new, exciting milestones."
  - "I want to empower my studio managers to run their own local challenges without my direct oversight."
- **Frustrations:**
  - "I feel disconnected from a key engagement feature of the platform I pay for."
  - "The current process makes me feel like I'm asking for permission to run my own business."
  - "I can't A/B test different milestone thresholds or rewards to see what really motivates my members."

---

## 3. User Stories

| As a...          | I want to...                                                                                                                              | So that...                                                                          |
| :--------------- | :---------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------- |
| **Client Admin** | access a secure, dedicated "Milestone Management" section of the application                                                              | I can manage my studio's milestones separately from my personal user profile.       |
| **Client Admin** | see a list of all my studio's current milestones with key details like name, trigger conditions, and status                               | I can get an at-a-glance overview of my current engagement strategy.                |
| **Client Admin** | create a new milestone by providing a name, description, trigger conditions (e.g., "attend 10 classes"), and rewards (e.g., "100 points") | I can expand my milestone offerings at any time without needing to contact support. |
| **Client Admin** | edit the details of an existing milestone                                                                                                 | I can correct mistakes, update offers, or change difficulty as my business evolves. |
| **Client Admin** | toggle a milestone between "Active" and "Inactive" status                                                                                 | I can temporarily hide seasonal or out-of-stock challenges without deleting them.   |
| **Client Admin** | delete a milestone permanently                                                                                                            | I can remove outdated challenges from my system completely.                         |

---

## 4. Functional Requirements

### 4.1. New Database Tables & Schema Changes

- **New Table: `milestones`**
  - `clientId`: `v.id('clients')` (indexed for multi-tenancy)
  - `name`: `v.string()`
  - `description`: `v.optional(v.string())`
  - `triggerType`: `v.string()` (e.g., "activity_count", initially the only supported type)
  - `conditions`: `v.object({ activityTypeMatcher: v.string(), countThreshold: v.number() })`
  - `rewards`: `v.array(v.object({ type: v.string(), value: v.union(v.string(), v.number()) }))` (e.g., `{type: "points", value: 100}`)
  - `isEnabled`: `v.boolean()` (default: `true`)
  - `isRepeatable`: `v.boolean()` (default: `false`)
  - `createdBy`: `v.id('users')` (for auditing)
  - `lastModifiedBy`: `v.id('users')` (for auditing)
- **Data Migration:** A one-time script will be created to migrate existing milestone definitions from the `clientConfiguration.milestones` array into the new `milestones` table. The `clientConfiguration.milestones` field will then be deprecated.

### 4.2. New Backend Functions

- **Mutations:**
  - `createMilestone(args)`: Creates a new document in the `milestones` table. Requires admin role.
  - `updateMilestone(args)`: Updates a document in the `milestones` table. Requires admin role.
  - `deleteMilestone({ milestoneId })`: Deletes a milestone. Requires admin role.
  - All mutations must validate that the user's `clientId` matches the milestone's `clientId`.
- **Queries:**
  - `getMilestonesForClient()`: Fetches all milestones for the admin's `clientId`.

### 4.3. Client Admin Dashboard UI

- **Access:** A new "Milestone Management" tab in the Admin Dashboard, visible only to users with an "admin" or "staff" role.
- **Main View:** A table displaying all milestones for the client.
  - **Columns:** `Name`, `Trigger`, `Reward`, `Status (Active/Inactive)`, `Actions`.
  - A prominent "Create New Milestone" button.
  - Each row has "Edit" and "Delete" actions.
- **Milestone Creation/Editing UI:** A modal form with the following fields:
  - **Name:** Text input, required.
  - **Description:** Textarea, optional.
  - **Trigger Type:** Dropdown (initially locked to "Activity Count").
  - **Activity Type:** Dropdown populated with the client's available activity types.
  - **Count Threshold:** Number input, required.
  - **Point Reward:** Number input, required.
  - **Is Enabled:** Toggle switch.

### 4.4. User Interaction Flow: Creating a Milestone

1.  Admin navigates to Admin Dashboard -> Milestone Management.
2.  Clicks "Create New Milestone".
3.  A modal appears with the milestone creation form.
4.  Admin fills out the form fields. The form provides real-time validation (e.g., count threshold must be > 0).
5.  Admin clicks "Save".
6.  The `createMilestone` mutation is called. The UI shows a loading state.
7.  Upon success, the modal closes, a success toast appears, and the milestone table refreshes to show the new milestone.
8.  The new milestone is immediately available to members if it was created with "Enabled" status.

---

## 5. Non-Functional Requirements

| Category               | Requirement                                                                                                                                                                             |
| :--------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability & Design** | The UI must be highly intuitive, consistent with the existing Rewards Management feature, and require no external documentation for a non-technical user to operate.                    |
| **Performance**        | The milestone list must load in **<1 second**. All create/update operations must complete in **<2 seconds**. UI feedback must be instantaneous.                                         |
| **Security**           | Role-Based Access Control (RBAC) is critical. Backend functions must rigorously verify the user's role and ensure they can only modify milestones associated with their own `clientId`. |
| **Accessibility**      | The UI must be WCAG 2.1 AA compliant, including full keyboard navigation and screen reader support for the form and table.                                                              |
| **Data Handling**      | The data migration script must be idempotent and run with zero data loss. All database operations must be atomic.                                                                       |

---

## 6. Out of Scope (for this PRD)

| Item                         | Rationale                                                                                                                                                  |
| :--------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Member-facing UI changes** | The focus is on the admin experience first. How members see and interact with these new milestones will be handled in a subsequent PRD.                    |
| **Advanced Milestone Types** | Time-based (e.g., streaks) or location-based triggers add significant complexity. We will launch with the most common type ("activity count") and iterate. |
| **Milestone Analytics**      | A dashboard for milestone performance is a valuable follow-up feature, but the priority is enabling creation and management first.                         |
| **Bulk Import/Export**       | We assume clients will manage milestones individually. Bulk tools can be added later based on demand.                                                      |

---

## 7. Success Metrics

| Metric                         | How to Measure                                                                                                              | Target Goal                       |
| :----------------------------- | :-------------------------------------------------------------------------------------------------------------------------- | :-------------------------------- |
| **Support Ticket Reduction**   | `# of manual milestone change requests per month`. Compare 3-month average before and after launch.                         | **>85% decrease** within 3 months |
| **Feature Adoption Rate**      | `(# of clients who create/edit a milestone / Total # of active clients) * 100`                                              | **>60%** within 60 days of launch |
| **Client Satisfaction (CSAT)** | A direct survey sent to all Client Admins: "How satisfied are you with the new Milestone Management tools?" (Scale of 1-5). | Average score **>4.2/5.0**        |

---

## 8. Open Questions & Assumptions

- **Assumption:** For MVP, the "activity_count" trigger type is sufficient to meet the majority of client needs.
- **Assumption:** Clients will not need to bulk-import a large number of milestones initially.
- **Question:** What are the most common activity types across our clients? This will inform the dropdown options in the creation form.
- **Question:** What should the validation rules be for the form fields (e.g., max point value, max character length for name/description)?
- **Question:** How should deleting a milestone affect members who have already made progress towards it? (Recommendation: Deletion should be a soft delete (`isEnabled: false`) if any user has progress, to avoid a negative user experience).
