# PRD: Anonymous-First Leaderboards with Beautiful Default Avatars

**Author:** <PERSON> (Product Manager)
**Version:** 1.0
**Status:** In Review

---

## 1. Introduction

### 1.1. Overview

This document outlines the requirements for the **Anonymous-First Leaderboards** feature. The core principle of this initiative is to prioritize user privacy and comfort by default. Instead of requiring users to publicly display their real names on leaderboards, we will provide them with a unique, aesthetically pleasing, and automatically generated anonymous identity. This identity consists of a creative, fitness-themed pseudonym and a beautiful, algorithmically-generated avatar that aligns with the client's brand.

This privacy-first approach aims to significantly lower the barrier to participation, making leaderboards a safe and engaging space for all members, especially those who are new or value their privacy. Users will always have the option to reveal their real identity when they feel comfortable, allowing for a gradual trust-building process.

### 1.2. Product Goals & Strategy

This feature is a key strategic initiative to foster a more inclusive and engaging community environment.

- **Establish Privacy as the Default:** Make users feel safe participating in leaderboards from day one without revealing personal information.
- **Increase Community Engagement:** Boost participation in leaderboards and social features (like "cheers") by removing the friction of public exposure.
- **Enhance Brand Integration:** Provide a visually cohesive experience by generating avatars that use each client's unique brand color palette, making the feature feel native to their studio.
- **Build User Trust:** Empower users with control over their identity, allowing them to reveal their real name only when they are ready.

---

## 2. Target Audience

### 2.1. Primary Audience: End Users

All platform users, with a specific focus on:

- **New Members:** Individuals who are just joining the fitness community and may be hesitant to share their performance publicly.
- **Privacy-Conscious Members:** Users who are motivated by competition and community but prefer not to link their fitness data to their real-world identity.

#### User Personas

**Persona 1: Cautious Competitor (New Member)**

- **Name:** Alex
- **Background:** Joined "The Handle Bar" a month ago. Is motivated by fitness goals but is introverted and hesitant to share progress publicly. Finds leaderboards intimidating.
- **Goals:**
  - To see how they rank against others without feeling exposed.
  - To feel part of the community in a low-pressure way.
- **Frustrations:**
  - "I don't want everyone in my class to see my name at the bottom of a leaderboard."
  - "I'm competitive, but I'm also private. I feel like I have to choose."
- **How this feature helps:** Alex can join the leaderboard immediately as "Aqua Racer" with a cool avatar, satisfying their competitive spirit without sacrificing privacy.

### 2.2. Secondary Audience: Client Administrator

- **Role:** Gym/Studio Owner, HR Manager (e.g., David from the system overview).
- **Goals:**
  - Increase member engagement and retention.
  - Ensure all platform features align with their established brand identity.
- **Frustrations:**
  - "Some features feel generic and don't match our studio's vibe."
  - "I want more members to use the app's community features, but many seem hesitant."
- **How this feature helps:** David can configure the anonymous avatars to use The Handle Bar's signature color palette. He can also customize the anonymous name themes to match the studio's fun, energetic tone, making the feature a seamless part of his brand.

---

## 3. User Stories

### Epic: Implement Anonymous-First Leaderboards

| ID        | User Story                                                                                                                                               | Role     | Priority  |
| --------- | -------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | --------- |
| **ANO-1** | As a new user, I want to be automatically anonymous on leaderboards so that I can participate without revealing my real name.                            | End User | Must-Have |
| **ANO-2** | As a user, I want to have a unique and visually appealing avatar that represents me anonymously, so my identity feels personal yet private.              | End User | Must-Have |
| **ANO-3** | As an anonymous user, I want a cool, memorable name that is automatically assigned to me so I don't have to think of one myself.                         | End User | Must-Have |
| **ANO-4** | As a user, I want the option to reveal my real identity on leaderboards later so that I can claim my achievements publicly when I feel comfortable.      | End User | Must-Have |
| **ANO-5** | As a user, I want my choice to be anonymous or public to be persistent across the app until I change it.                                                 | End User | Must-Have |
| **ANO-6** | As an anonymous user, I want to interact with others (e.g., cheer, high-five) without revealing my identity so that I can engage socially in a safe way. | End User | High      |
| **ANO-7** | As a client administrator, I want the anonymous avatars to use my brand's colors so that the feature feels integrated with our studio's look and feel.   | Admin    | Must-Have |
| **ANO-8** | As a client administrator, I want to customize the themes for anonymous name generation to match our brand's voice.                                      | Admin    | High      |
| **ANO-9** | As a client administrator, I want an easy way to define our brand's avatar color palette in the admin settings.                                          | Admin    | Must-Have |

---

## 4. Functional Requirements

### 4.1. User Identity & Anonymity

- **FR1: Default Anonymity:** All new users must default to an anonymous state for all leaderboard and public-facing social features.
- **FR2: Stable Anonymous Identity:**
  - On user creation, the system must generate and permanently store a unique `avatarSeed` (UUID format) for the user. This seed will be used to generate a consistent avatar.
  - On user creation, the system must generate and store a stable `anonymousName` (e.g., "Golden Warrior").
- **FR3: User Control:**
  - Users must have a clear and easily accessible setting in their profile or dashboard to switch their leaderboard visibility between "Anonymous" and "Public."
  - This setting will toggle a `showRealName: boolean` flag in their user profile.
- **FR4: Data Schema:** The `users` table/document must be updated to include a `leaderboardSettings` object:
  ```typescript
  leaderboardSettings: v.object({
    showRealName: v.boolean(), // Defaults to false
    anonymousName: v.string(),
    avatarSeed: v.string(), // UUID
  });
  ```

### 4.2. Avatar Generation & Display

- **FR5: Avatar Library:** The system must use the `boring-avatars` library to generate avatars.
- **FR6: Avatar Style:** The avatar generation must specifically use the `bauhaus` variant from the library.
- **FR7: Client-Branded Colors:**
  - The colors used for avatar generation must be dynamically pulled from an array in the client's configuration: `clientConfiguration.branding.avatarColors`.
  - This array should contain a list of hex color codes (e.g., `['#D4A574', '#CD7F32', '#F5F2E8']`).
  - If `avatarColors` is not configured for a client, a visually appealing default palette must be used.
- **FR8: Avatar Display:**
  - All UI components displaying a user in a leaderboard context (e.g., `LeaderboardList`, `ProfileCard`) must conditionally render the user's identity.
  - If `user.leaderboardSettings.showRealName` is `false`, display the `boring-avatars` component using the user's `avatarSeed` and `anonymousName`.
  - If `user.leaderboardSettings.showRealName` is `true`, display the user's real `profileImageUrl` (from Clerk) and `firstName` / `lastName`.

### 4.3. Anonymous Name Generation

- **FR9: Name Generation Logic:** The system will combine adjectives and nouns from predefined, fitness-themed lists to create names (e.g., "Steady" + "Climber").
- **FR10: Admin Customization:**
  - A new `anonymousNameTemplates` table/collection will be created.
  - Schema: `{ clientId: v.id('clients'), themeName: v.string(), adjectives: v.array(v.string()), nouns: v.array(v.string()) }`
  - Client Admins will have a UI in `Admin > Settings` to create and manage these templates (e.g., a "Yoga" theme with words like "Zen," "Flow," "Warrior").
  - If no custom theme is set, a system-wide default theme will be used.

### 4.4. UI/UX

- **FR11: Leaderboard Display:** Leaderboard rows will display the user's avatar, name (anonymous or real), rank, and points.
- **FR12: Tooltips:** On hover, avatars should have a tooltip displaying the user's full anonymous name.
- **FR13: Social Interactions:** When a user "cheers" another user, the notification and display will show the anonymous identity of the cheering user if they are anonymous.

---

## 5. Non-Functional Requirements

| Category               | Requirement                                                                                                                                                                                                                          |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Usability & Design** | Anonymous avatars and names must be visually appealing and feel like a first-class feature, not a placeholder. The UI for switching between anonymous and public must be intuitive and provide clear feedback.                       |
| **Performance**        | Avatar generation with `boring-avatars` must be client-side and fast, with no perceivable lag when rendering leaderboards. Anonymous name generation on the backend should not add significant latency to the user creation process. |
| **Accessibility**      | Avatars should have appropriate `alt` tags or `aria-label` attributes describing the anonymous identity (e.g., "Avatar for Golden Warrior").                                                                                         |
| **Security**           | The `avatarSeed` should be a non-guessable UUID to ensure privacy. User preference for anonymity must be respected across all current and future public-facing features.                                                             |
| **Data Handling**      | The database migration to add the `leaderboardSettings` object to existing users must be handled gracefully, backfilling a unique `avatarSeed` and `anonymousName` for every user.                                                   |

---

## 6. Out of Scope

- Users uploading their own custom anonymous avatars.
- Users choosing their own anonymous name.
- More than one avatar style (feature is locked to `bauhaus` for V1).
- Leaderboard filtering or sorting based on anonymity.
- Publicly displaying a user's real name and anonymous name simultaneously.

---

## 7. Success Metrics

| Metric                            | How We'll Measure It                                                                                                                                                                | Target                                                                                                     |
| --------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- |
| **Anonymous Participation Rate**  | Track the percentage of monthly active users with `showRealName: false`. We will query the `users` table daily and aggregate the results.                                           | **85%** of active users remaining anonymous after 30 days.                                                 |
| **Identity Reveal Rate**          | Track the percentage of users who switch `showRealName` from `false` to `true`. This will be tracked via an analytics event fired when the setting is changed.                      | **15%** of users revealing their identity after 30 days, increasing to **25%** after 90 days.              |
| **Anonymous Social Interactions** | Log an event for each "cheer" or similar interaction, including a flag for whether the instigator was anonymous. We will calculate the average number of cheers per anonymous user. | Average of **8** "cheers" per anonymous user/week within 30 days, increasing to **15**/week after 90 days. |
| **Avatar Satisfaction Rating**    | Implement a one-time, non-intrusive in-app survey asking users to rate their anonymous avatar on a scale of 1-5 after they have viewed the leaderboard 3 times.                     | Achieve an average rating of **4.2 / 5.0** within 30 days of launch.                                       |

---

## 8. Open Questions / Assumptions

- **Assumption:** The `boring-avatars` library is stable, performant, and will remain maintained.
- **Assumption:** Clients will find value in customizing anonymous name themes and will provide creative, brand-aligned content.
- **Question:** Should we pre-generate a batch of anonymous names to ensure uniqueness, or is the risk of collision with an adjective/noun combination low enough to generate on-the-fly? (Initial proposal: generate on-the-fly and check for duplicates within the client's user base).
- **Question:** What is the desired behavior if a Client Admin deletes a custom name theme that is currently in use by members? (Initial proposal: users retain their existing name, new users fall back to the default theme).
