# Product Requirements Document: Rewards Catalog & Redemption System

## 1. Introduction

### 1.1. Overview & Product Strategy

The **Rewards Catalog & Redemption System** is a cornerstone feature designed to complete the core gamification loop of the FitRewards platform. Currently, users can earn points for their fitness activities, but they have no way to spend them. This feature introduces a tangible value proposition for point accumulation, allowing users to exchange their hard-earned points for real-world rewards defined by our clients (e.g., free smoothies, merchandise discounts, class packs).

By closing the `Act -> Earn -> Redeem` loop, we directly support our strategic goals of increasing user engagement, driving long-term retention, and providing a direct revenue-driving tool for our B2B clients.

### 1.2. Feature Goals

- **Close the Gamification Loop:** Provide a meaningful outlet for users to spend their points, making the entire system more compelling.
- **Drive Business Value for Clients:** Enable fitness studios to promote and sell ancillary products and services (e.g., smoothies, apparel, personal training sessions).
- **Increase User Engagement:** Create aspirational goals for users, encouraging them to interact with the platform more frequently to check their point balance and browse rewards.
- **Enhance Retention:** Leverage the "near-miss" effect, where users who are close to a desired reward are motivated to continue their fitness journey to close the gap.

---

## 2. Target Audience

### 2.1. Primary User Persona: The Engaged Enthusiast

- **Name:** Sarah, the Motivated Member
- **Role:** A dedicated member of a fitness studio.
- **Demographics:** 28-40, urban professional, tech-savvy.
- **Behaviors:**
  - Attends 3-5 classes per week.
  - Is intrinsically motivated by fitness but loves seeing progress quantified.
  - Engages with the community and follows the studio on social media.
- **Goals & Motivations:**
  - "I want my hard work and consistency to be recognized."
  - "I love feeling like I'm earning something extra for a routine I already enjoy."
  - "A free smoothie or a discount on a new tank top would be a great bonus and make me feel valued."
- **Frustrations:**
  - "I'm accumulating all these points, but what are they for? They feel meaningless right now."

### 2.2. Secondary User Persona: The Business Owner

- **Name:** David, the Studio Owner
- **Role:** Client Administrator for a fitness studio.
- **Behaviors:**
  - Manages studio operations, marketing, and member experience.
  - Is focused on community building and revenue growth.
  - Wants tools that are easy to manage and provide clear ROI.
- **Goals & Motivations:**
  - "I want to increase sales at our smoothie bar and move more merchandise."
  - "I need a way to reward my most loyal members without just giving away free classes."
  - "I want to create a vibrant, engaging community that keeps members coming back."
- **Frustrations:**
  - "Generic discount campaigns don't feel personal and often cheapen the brand."
  - "I don't have time to manage a complex, new system. It needs to integrate seamlessly."

---

## 3. User Stories

### 3.1. End User (Sarah)

| As a...  | I want to...                                                                                             | So that...                                                       |
| :------- | :------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------- |
| **User** | browse a visually appealing catalog of available rewards                                                 | I can see what I can spend my points on and get motivated.       |
| **User** | clearly see the point cost of each reward and my current point balance on the same screen                | I can easily determine what I can afford.                        |
| **User** | see some rewards as "locked" or "unaffordable" if I don't have enough points                             | I know what to save up for.                                      |
| **User** | redeem a reward with a simple, two-click confirmation process                                            | I can quickly and easily claim my prize without friction.        |
| **User** | receive immediate confirmation that my redemption was successful and see my points balance decrease      | I have confidence the transaction worked.                        |
| **User** | view a history of all the rewards I have redeemed in the past                                            | I can track what I've earned and feel a sense of accomplishment. |
| **User** | receive an error message if a redemption fails for any reason (e.g., insufficient points on final check) | I know something went wrong and that my points were not spent.   |

### 3.2. Client Administrator (David)

| As a...          | I want to...                                                              | So that...                                                         |
| :--------------- | :------------------------------------------------------------------------ | :----------------------------------------------------------------- |
| **Client Admin** | ensure the rewards catalog is specific to my fitness studio and brand     | I can control my own offerings, costs, and inventory.              |
| **Client Admin** | define rewards with a name, point cost, description, and image            | I can create an attractive and informative catalog for my members. |
| **Client Admin** | have a reliable system that securely handles the point transaction        | I can trust that the system won't create errors or allow fraud.    |
| **Client Admin** | (Implicit) have a simple way for my staff to verify a member's redemption | we can fulfill the reward in-studio confidently.                   |

---

## 4. Functional Requirements

### 4.1. Rewards Catalog UI

- A new, dedicated "Rewards" page shall be added to the user-facing application, accessible from the main navigation.
- The page will display a grid or list of all rewards configured for that user's client.
- Each reward card will display:
  - Reward Name (e.g., "Free Smoothie")
  - Point Cost (e.g., "1,000 Points")
  - Reward Image (if provided in config)
  - Short Description (optional)
- The user's current point balance shall be prominently displayed on this page.
- Rewards that the user cannot afford will be visually distinct (e.g., grayed out, with a "locked" icon) but still visible to serve as a motivational goal.
- Clicking a reward will open a detail modal or view.

### 4.2. Redemption Flow

1.  **Initiation:** From the reward detail view, the user clicks a "Redeem" button. This button must be disabled if the user has insufficient points.
2.  **Confirmation:** A confirmation modal appears, summarizing the redemption.
    - Text: "Redeem [Reward Name] for [Point Cost] points? Your new balance will be [New Balance]."
    - Actions: "Confirm" and "Cancel" buttons.
3.  **Transaction:**
    - Clicking "Confirm" triggers the `redeemReward` backend mutation.
    - The UI enters a loading state.
4.  **Feedback:**
    - **On Success:** The UI displays a success message (e.g., "Success! Show this screen at the front desk to claim your reward."). The user's point balance on the screen updates immediately. The user is redirected to their Redemption History page.
    - **On Failure:** The UI displays an error message (e.g., "Redemption failed. Please try again."). The user's point balance does not change.

### 4.3. Redemption History

- A "History" tab or section will be available on the Rewards page.
- This view will list all of the user's past redemptions in reverse chronological order.
- Each entry will show:
  - Reward Name
  - Point Cost at time of redemption
  - Date of redemption

### 4.4. Backend Logic

- **Data Model:** A new `userRedemptions` table will be created in the database with fields such as `userId`, `clientId`, `rewardName`, `pointsSpent`, and `redemptionTimestamp`.
- **Catalog Source:** The available rewards for a client will be sourced from the existing `clientConfiguration.rewards` array.
- **`redeemReward` Mutation:** A new, secure backend mutation is required.
  - **Input:** `rewardId` (or a unique identifier for the reward being redeemed).
  - **Logic:**
    1.  Authenticates the user session.
    2.  Fetches the user's current points and the details of the requested reward from the client's configuration.
    3.  Verifies that `user.points >= reward.cost`.
    4.  In a single, **atomic transaction**:
        - Decrements the user's points in the `users` table.
        - Creates a new record in the `userRedemptions` table.
    5.  Returns a success or failure status.

---

## 5. Non-Functional Requirements

| Category               | Requirement                                                                                                                                                                                          |
| :--------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability & Design** | The UI must be intuitive, self-explanatory, and require minimal clicks to complete a redemption. The design must be clean, modern, and consistent with the client's branding and style guide.        |
| **Performance**        | The end-to-end `redeemReward` mutation (from user click to UI feedback) must complete in **under 500ms** on a stable connection. Browsing the catalog should feel instantaneous.                     |
| **Security**           | The redemption process must be secure against race conditions. A user must not be able to spend the same points twice by sending rapid, duplicate requests. All API endpoints must be authenticated. |
| **Data Handling**      | The debiting of points and the creation of a redemption record must be an **atomic transaction**. If one part fails, the entire operation must roll back to prevent data inconsistency.              |
| **Reliability**        | The system should be highly available. In case of failure, it should fail gracefully with clear error messages to the user.                                                                          |
| **Observability**      | The system must log successful and failed redemptions on the backend to allow for monitoring and debugging.                                                                                          |

---

## 6. Out of Scope (for MVP)

The following items are explicitly excluded from the initial release to ensure a focused and timely delivery:

- **Admin UI for Reward Management:** Clients will not have a self-service UI to add, edit, or remove rewards. All reward catalog management will be done via database seed scripts by the platform team.
- **Fulfillment Tracking:** The system will only track the act of redemption. It will not track whether the user has actually picked up their smoothie or used their discount (i.e., no "Mark as Fulfilled" status).
- **QR Codes / Redemption Codes:** The MVP will not generate unique codes for verification. Verification will be a manual process (e.g., user shows the success screen on their phone to front-desk staff).
- **Time-boxed or Limited-Quantity Rewards:** All rewards are considered to be available at all times and in unlimited quantities.

---

## 7. Success Metrics

| Metric                | Target   | How to Measure                                                                                                                        |
| :-------------------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------ |
| **Redemption Rate**   | >15%     | `(Unique users who redeemed at least one reward in a month / Monthly Active Users) * 100`                                             |
| **Points Sink Ratio** | >25%     | `(Total points redeemed in a period / Total points earned in the same period) * 100`. This measures the health of the points economy. |
| **Feature Adoption**  | >70%     | `(Unique users who visit the Rewards page in a month / Monthly Active Users) * 100`                                                   |
| **User Satisfaction** | Positive | Qualitative feedback gathered via user interviews, support tickets, and in-app surveys (post-MVP).                                    |

---

## 8. Open Questions & Assumptions

- **Assumption:** For the MVP, having a user show their phone with the "Success" screen or their redemption history is a sufficient verification method for studio staff to fulfill a reward.
- **Question:** How will we communicate the initial process for reward catalog setup to clients, given the admin UI is out of scope?
- **Question:** Is an "undo" or "refund" feature needed for accidental redemptions? (Initial stance: No, for MVP, all redemptions are final).
- **Question:** What is the desired user experience if a reward is removed from the configuration after a user has redeemed it? How should it appear in their history?
- **Question:** Should there be a platform-level notification (e.g., email) to the user upon successful redemption? (Initial stance: No, in-app confirmation is sufficient for MVP).
