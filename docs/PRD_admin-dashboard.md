# Product Requirements Document: Client Administrator Dashboard & Rewards Management

**Document Version:** 1.0  
**Date:** [Current Date]  
**Author:** Product Management Team  
**Status:** Approved for Development

---

## 1. Introduction

### 1.1. Feature Overview & Strategic Alignment

The **Client Administrator Dashboard & Rewards Management** feature is the first major expansion of our self-service capabilities for B2B clients. It provides a dedicated administrative interface where clients can independently manage their rewards catalog and verify user redemptions.

This feature directly aligns with our core product strategy to **increase client autonomy and reduce operational dependency on the platform team**. By empowering studio owners and managers to create, update, and manage their own rewards, we:

- **Empower Clients:** Enable dynamic, real-time reward strategies to drive member engagement and secondary revenue (e.g., merchandise, concessions).
- **Reduce Operational Overhead:** Significantly decrease the volume of support tickets and manual engineering effort required for reward updates.
- **Close the Fulfillment Loop:** Introduce a formal mechanism for front-desk staff to verify and track reward fulfillment, preventing fraud and providing valuable business data.
- **Establish an Admin Foundation:** Create the architectural and UI foundation for all future client-facing administrative features, such as milestone management, user analytics, and dashboard customization.

### 1.2. Problem Statement

**Current State:** All reward catalog changes (adding, editing, or removing rewards) require a manual request to our support team, who then must execute database scripts. This process is slow, inefficient, and error-prone. Furthermore, there is no formal system for studio staff to verify that a reward has been claimed, leading to potential abuse and a lack of fulfillment data.

**Client Pain Points:**

- "I have a great idea for a weekend-only reward, but by the time I can get it added, the weekend is over."
- "I made a typo in the reward description I sent over, and now I have to submit another ticket to fix it."
- "I'm not sure if a member has already claimed their free smoothie. We're relying on the honor system."

**Business Impact:** High operational costs due to manual engineering work, a poor client experience with long lead times for changes, and lost revenue opportunities for our clients.

### 1.3. Vision for Success

**Target State:** Client administrators can log in to a secure dashboard, manage their entire rewards catalog with a few clicks, and empower their staff with a simple tool to verify redemptions. This seamless workflow allows clients to be more agile, creative, and data-driven with their rewards strategy, while our internal team is freed up to focus on building new platform-wide features.

---

## 2. Target Audience

### 2.1. Primary Persona: "David" - The Empowered Studio Owner (Client Administrator)

- **Role:** Owner of "The Handle Bar," a boutique fitness studio. He wears multiple hats, from business strategy and marketing to managing staff and member relations.
- **Demographics:** 35-50, business-oriented, moderately tech-savvy but values simplicity and efficiency.
- **Goals & Motivations:**
  - "I want to increase our non-membership revenue streams, like our smoothie bar and apparel."
  - "I need tools that my staff can learn quickly without extensive training."
  - "I want to react quickly to member feedback and market trends with new, exciting rewards."
- **Frustrations:**
  - "I hate being dependent on another company's timeline to update my own business offerings."
  - "I don't have time for complex software; it needs to be intuitive and just work."

### 2.2. Secondary Persona: "Maria" - The Efficient Front-Desk Lead (Staff Member)

- **Role:** Front-desk manager at "The Handle Bar." She is the primary point of contact for members and is responsible for class check-ins, sales, and ensuring a smooth member experience.
- **Demographics:** 22-30, customer-service oriented, proficient with tablets and POS systems.
- **Goals & Motivations:**
  - "I want to serve members quickly and efficiently, especially during busy check-in times."
  - "I need a clear and unambiguous way to confirm a member's reward redemption."
  - "I want to avoid conflict or confusion over whether a reward has already been claimed."
- **Frustrations:**
  - "It's awkward to challenge a member who says they have a reward to claim."
  - "Juggling multiple systems during check-in is stressful and leads to mistakes."

---

## 3. User Stories

### 3.1. Client Administrator Stories (David)

| As a...          | I want to...                                                                                | So that...                                                                         |
| :--------------- | :------------------------------------------------------------------------------------------ | :--------------------------------------------------------------------------------- |
| **Client Admin** | access a secure, dedicated "Admin" section of the application                               | I can manage my studio's configuration separately from my personal user profile.   |
| **Client Admin** | see a list of all my studio's current rewards with key details like name, cost, and status. | I can get an at-a-glance overview of my current offerings.                         |
| **Client Admin** | create a new reward by providing a name, point cost, description, and an optional image.    | I can expand my rewards catalog at any time without needing to contact support.    |
| **Client Admin** | edit the details of an existing reward (name, cost, description, image).                    | I can correct mistakes, update offers, or change pricing as my business evolves.   |
| **Client Admin** | toggle a reward between "Active" and "Inactive" status.                                     | I can temporarily hide seasonal or out-of-stock rewards without deleting them.     |
| **Client Admin** | delete a reward permanently.                                                                | I can remove outdated offers from my system completely.                            |
| **Client Admin** | see a list of recent redemptions from my members.                                           | I can monitor which rewards are popular and how frequently they are being claimed. |
| **Client Admin** | search for a specific member by name or email within the admin dashboard.                   | I can look up a member's redemption history to resolve any disputes.               |

### 3.2. Front-Desk Staff Stories (Maria)

| As a...              | I want to...                                                                                                 | So that...                                                                                   |
| :------------------- | :----------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------- |
| **Front-Desk Staff** | look up a member who is in front of me to claim a reward.                                                    | I can quickly find their profile and list of pending redemptions.                            |
| **Front-Desk Staff** | see a list of a member's unfulfilled rewards.                                                                | I know exactly what they are eligible to claim.                                              |
| **Front-Desk Staff** | click a single button next to a redeemed reward to mark it as "Fulfilled".                                   | I can prevent duplicate claims and officially track that the member has received their item. |
| **Front-Desk Staff** | see a clear visual confirmation after a reward is marked as fulfilled (e.g., it moves to a "Fulfilled" tab). | I have confidence that the action was successful and cannot be accidentally repeated.        |
| **Front-Desk Staff** | have the system record who fulfilled the reward and when.                                                    | the business has a clear audit trail for fulfillment.                                        |

---

## 4. Functional Requirements

### 4.1. New Database Tables & Schema Changes

- **New Table: `rewards`**

  - `clientId`: `v.id('clients')` (indexed)
  - `name`: `v.string()`
  - `description`: `v.optional(v.string())`
  - `cost`: `v.number()` (points)
  - `imageUrl`: `v.optional(v.string())`
  - `isActive`: `v.boolean()` (default: `true`)

- **Table Update: `userRedemptions`**
  - `rewardId`: `v.id('rewards')` (replaces `rewardName` and `pointsSpent`)
  - `status`: `v.string()` (Enum: "Pending", "Fulfilled"; default: "Pending"; indexed)
  - `fulfilledAt`: `v.optional(v.number())`
  - `fulfilledBy`: `v.optional(v.id('users'))` (Clerk ID of the staff member)

### 4.2. New Backend Functions

- **Mutations:**
  - `createReward(args)`: Creates a new document in the `rewards` table. Requires admin role.
  - `updateReward(args)`: Updates a document in the `rewards` table. Requires admin role.
  - `deleteReward({ rewardId })`: Deletes a reward. Requires admin role.
  - `fulfillRedemption({ redemptionId })`: Updates a `userRedemptions` document's status to "Fulfilled" and populates `fulfilledAt` and `fulfilledBy`. Requires staff or admin role.
- **Queries:**
  - `getRewardsForClient()`: Fetches all rewards for the admin's `clientId`.
  - `getRedemptionsForClient(args)`: Fetches user redemptions, with filters for status (Pending/Fulfilled) and user search.
  - `findUsersByNameOrEmail({ searchQuery })`: A utility for the admin panel to look up users.

### 4.3. Client Admin Dashboard UI

- **Access:** A new "Admin" link in the main navigation, visible only to users with an "admin" or "staff" role (defined in Clerk session claims).
- **Main View:** A tabbed interface.
  - **Tab 1: Rewards Management**
    - Displays a table/list of all rewards for the client.
    - Columns: `Name`, `Cost`, `Status (Active/Inactive)`, `Actions`.
    - A prominent "Create New Reward" button.
    - Each row has "Edit" and "Delete" actions.
  - **Tab 2: Redemption Fulfillment**
    - A search bar to find members by name or email.
    - A list of recent redemptions with "Pending" status by default.
    - Columns: `Member Name`, `Reward Name`, `Date Redeemed`.
    - Clicking a row expands to show a "Mark as Fulfilled" button.

### 4.4. Reward Creation/Editing UI

- A modal or dedicated page with a form for creating/editing rewards.
- **Fields:**
  - Name (text input, required)
  - Point Cost (number input, required)
  - Description (textarea, optional)
  - Image URL (text input, optional)
  - Is Active (toggle switch)
- Form validation must be present.

### 4.5. Redemption Fulfillment Flow

1.  Staff member navigates to the Admin Dashboard -> Redemption Fulfillment tab.
2.  Searches for the member by name (e.g., "Sarah").
3.  The system displays a list of matching members. Staff clicks the correct Sarah.
4.  The UI shows Sarah's profile with a list of her "Pending" redemptions.
5.  Staff identifies the correct reward (e.g., "Free Smoothie") and clicks "Mark as Fulfilled".
6.  A confirmation modal appears: "Fulfill 'Free Smoothie' for Sarah?".
7.  Upon confirmation, the backend `fulfillRedemption` mutation is called.
8.  The UI provides immediate feedback, the button becomes disabled, and the item moves from the "Pending" list to a "Fulfilled" list for that user.

---

## 5. Non-Functional Requirements

| Category               | Requirement                                                                                                                                                                                                       |
| :--------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability & Design** | The admin interface must be extremely intuitive for non-technical users. It should be clean, uncluttered, and require no training. All actions should have clear labels and confirmation steps to prevent errors. |
| **Performance**        | The rewards list and redemption history must load in under 1 second, even for clients with hundreds of rewards or thousands of redemptions. The user search must return results in under 500ms.                   |
| **Security**           | Role-based access control (RBAC) is critical. Backend functions must rigorously verify the user's role (admin/staff) from their authenticated session before executing any mutation.                              |
| **Accessibility**      | The admin dashboard must be WCAG 2.1 AA compliant, with full keyboard navigation and screen reader support for all forms and actions.                                                                             |
| **Data Handling**      | Migrating existing rewards from the `clientConfiguration` file into the new `rewards` table must be handled with a zero-downtime data migration script. All redemptions must be atomic operations.                |

---

## 6. Out of Scope (for this PRD)

- **User-Facing Fulfillment Status:** The end-user (member) will NOT see the "Pending" vs. "Fulfilled" status in this phase. Their view of the redemption history remains unchanged.
- **Client-Facing Milestone Management:** This dashboard is only for rewards. Milestone management remains a manual process.
- **Advanced Analytics:** No dashboards, charts, or reports on reward performance or fulfillment rates will be built.
- **Inventory Management:** The system will not track the quantity of available rewards.
- **Role Management UI:** Assigning "admin" or "staff" roles will be handled manually in the Clerk dashboard, not within our application's UI.

---

## 7. Success Metrics

| Metric                         | How to Measure                                                                                                                                           | Target Goal        |
| :----------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| **Feature Adoption Rate**      | `(# of clients who use the admin dashboard to create/edit a reward in the first 30 days / Total # of clients) * 100`                                     | >80%               |
| **Support Ticket Reduction**   | `(# of manual reward change requests per month)`. Compare the 3-month average before and after launch.                                                   | >90% decrease      |
| **Fulfillment Rate**           | `(# of redemptions marked "Fulfilled" in a period / Total # of redemptions in that period) * 100`                                                        | >70%               |
| **Client Satisfaction (CSAT)** | A direct survey sent to all Client Admins 60 days post-launch asking: "How satisfied are you with the new Rewards Management dashboard?" (Scale of 1-5). | Average score >4.5 |

---

## 8. Open Questions & Assumptions

- **Assumption:** For the MVP, a simple "admin" and "staff" role distinction is sufficient. We assume admins can do everything staff can do.
- **Question:** What should the data migration strategy be for rewards currently defined in the `clientConfiguration.rewards` array? Do we create a new `rewards` collection and deprecate the old field? (Recommendation: Yes, run a one-time migration script).
- **Question:** How should deleting a reward affect past `userRedemptions` that reference it? (Recommendation: Deleting a reward is a soft delete (`isActive: false`) or we store the reward details denormalized on the redemption record).
- **Question:** What is the desired UX for the image upload/management? Is it a URL paste, or do we need to integrate an asset storage service? (Recommendation: URL paste for MVP).
- **Question:** Should there be a limit to the number of rewards a client can create? (Recommendation: No limit for MVP, but monitor for performance).
- **Question:** What happens if a staff member who fulfilled a redemption is later deleted from the system? How is `fulfilledBy` handled? (Recommendation: We store the Clerk `userId`, and if that user is gone, the name is no longer resolvable, which is acceptable).

---

**End of Document**
