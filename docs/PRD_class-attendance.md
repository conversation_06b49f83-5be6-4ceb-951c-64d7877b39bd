# Log Class Attendance Activity & First Class Milestone - Product Requirements Document (PRD)

**Document Version:** 1.0  
**Date:** June 6, 2025  
**Author:** Product Management Team  
**Status:** Draft for Development Review

---

## 1. Introduction

### 1.1 Feature Overview

The "Log Class Attendance Activity & First Class Milestone" feature represents the first complete implementation of the FitRewards platform's core gamification loop: **User Action → Business Logic → Reward**. This feature enables fitness studio members to log their class attendance, earn points, and unlock their first milestone achievement, creating an immediate sense of accomplishment and engagement.

### 1.2 Strategic Importance

This feature serves as the foundational building block for the entire platform, establishing:

- **End-to-End Validation:** Complete technology stack validation from frontend interaction to database persistence
- **Engagement Foundation:** The primary mechanism for user retention and motivation
- **Technical Precedent:** Design patterns and architecture that will be replicated across all future gamification features
- **Business Value Proof:** Demonstrable ROI for fitness businesses through increased member engagement

### 1.3 Success Criteria

- Users can successfully log class attendance with immediate visual feedback
- Points are accurately awarded and stored in real-time
- Milestone achievements trigger automatically upon meeting conditions
- All data updates reflect instantly across the user interface via Convex reactivity

---

## 2. Target Audience

### 2.1 Primary User Persona: "William" - The Engaged Fitness Enthusiast

**Demographics:**

- Age: 28-45
- Occupation: Working professional
- Fitness Level: Beginner to intermediate
- Technology Comfort: High (smartphone native)

**Behavioral Profile:**

- Attends fitness classes 2-4 times per week
- Values progress tracking and achievement recognition
- Motivated by goal setting and milestone completion
- Enjoys sharing fitness accomplishments
- Responds positively to gamification elements

**Goals & Motivations:**

- Track fitness progress consistently
- Feel recognized for attendance and effort
- Build sustainable exercise habits
- Connect with the fitness community
- Achieve tangible milestones and rewards

**Pain Points:**

- Difficulty maintaining motivation without visible progress
- Lack of recognition for consistency
- Forgetting to track activities manually
- Unclear path to earning rewards or benefits

**Technology Usage:**

- Smartphone primary device for fitness apps
- Comfortable with one-click actions
- Expects real-time feedback and instant gratification
- Values simple, intuitive user interfaces

---

## 3. User Stories

### 3.1 Primary Success Story

**User Story #1: Successful Class Attendance Logging**

```
As William (a fitness studio member),
After I attend a fitness class,
I want to log my attendance in the app with one click,
So that I can earn points and track my progress toward achievements.
```

**Acceptance Criteria:**

- ✅ A clearly labeled "Log Class Attendance" button is prominently displayed on the dashboard
- ✅ Clicking the button triggers immediate visual feedback (loading state)
- ✅ Upon successful logging, user points are updated and displayed in real-time
- ✅ Success confirmation message appears with points earned
- ✅ Button becomes temporarily disabled to prevent duplicate logging
- ✅ All UI updates occur without page refresh using Convex reactivity

### 3.2 First Milestone Achievement Story

**User Story #2: First Class Milestone Unlock**

```
As William (a new fitness studio member),
When I log my very first class attendance,
I want to automatically unlock the "First Steps at Studio A" milestone,
So that I feel welcomed and motivated to continue my fitness journey.
```

**Acceptance Criteria:**

- ✅ First class attendance automatically triggers milestone evaluation
- ✅ "First Steps at Studio A" milestone unlocks immediately upon first logging
- ✅ User receives 100 bonus points for milestone completion
- ✅ Badge achievement is awarded and visible in user profile
- ✅ Celebratory notification appears with milestone details
- ✅ Progress indicators update to reflect milestone completion

### 3.3 Error Handling Stories

**User Story #3: Authentication Required**

```
As an unauthenticated user,
When I attempt to access the dashboard,
I should be redirected to the sign-in page,
So that only registered members can log activities.
```

**Acceptance Criteria:**

- ✅ Unauthenticated users cannot access dashboard functionality
- ✅ Protected routes redirect to authentication flow
- ✅ Clear messaging explains authentication requirement

**User Story #4: Network Error Handling**

```
As William,
When I attempt to log class attendance but encounter a network error,
I want to see a clear error message and retry option,
So that I don't lose my activity logging attempt.
```

**Acceptance Criteria:**

- ✅ Network errors display user-friendly error messages
- ✅ Loading states clear appropriately on error
- ✅ Button re-enables for retry attempts
- ✅ Error messages provide actionable guidance

---

## 4. Functional Requirements

### 4.1 Frontend User Interface Requirements

**Dashboard Integration:**

- **Location:** Primary dashboard page (`/dashboard`)
- **Component:** Prominent "Log Class Attendance" button or card
- **Visual Design:**
  - Large, accessible button with clear labeling
  - Primary brand color (customizable per client)
  - Icon representation (e.g., calendar check, dumbbell)
- **Positioning:** Above-the-fold, high visual hierarchy

**Interactive Behavior:**

- **Default State:** Enabled button with "Log Class Attendance" text
- **Loading State:** Disabled button with spinner and "Logging..." text
- **Success State:** Brief confirmation with checkmark, then return to default
- **Cooldown State:** Temporarily disabled with "Logged!" text (prevents spam)
- **Error State:** Red border/text with error message display

**Real-time Updates:**

- Points counter updates immediately upon successful logging
- Milestone notifications appear as modal or toast
- Progress indicators reflect new activity count
- Activity history updates with new entry

### 4.2 Backend Process Flow

**Mutation Function: `logClassAttendance`**

1. **Authentication Validation**

   - Verify JWT token validity
   - Extract Clerk user ID from token
   - Lookup corresponding user record in database

2. **Activity Recording**

   - Create new activity record with:
     - User ID reference
     - Activity type: "class_attendance"
     - Timestamp: Current server time
     - Client ID: User's associated fitness studio

3. **Milestone Service Integration**

   - Call `MilestoneService.evaluateUserMilestones(userId, activityType)`
   - Process milestone rules from database configuration
   - Determine newly achieved milestones

4. **Reward Processing**

   - Calculate base points for class attendance (configurable per client)
   - Add milestone bonus points for any newly achieved milestones
   - Update user's total points atomically

5. **Response Generation**
   - Return success response with:
     - Points earned (base + milestone bonuses)
     - Newly achieved milestones
     - Updated user totals
     - Activity confirmation

### 4.3 Core Logic - Milestone Service

**Location:** `packages/core/src/services/MilestoneService.ts`

**Function: `evaluateUserMilestones`**

```typescript
interface MilestoneEvaluationInput {
  userId: string;
  activityType: string;
  userActivities: Activity[];
  availableMilestones: Milestone[];
}

interface MilestoneEvaluationResult {
  newlyAchieved: Milestone[];
  pointsAwarded: number;
  badgesEarned: string[];
}
```

**First Class Milestone Logic:**

- **Trigger:** `activityType === "class_attendance"`
- **Condition:** User's total `class_attendance` count equals 1
- **Rewards:**
  - 100 points
  - Badge: "badge_first_class_studio"
- **Repeatable:** No (one-time achievement)

**Implementation Requirements:**

- Pure functions for testability
- No database dependencies (data passed as parameters)
- Immutable operations
- Clear separation of business logic from data access

### 4.4 Database Schema Extensions

**New Table: `activities`**

```typescript
activities: defineTable({
  userId: v.id('users'),
  clientId: v.id('clients'),
  activityType: v.string(),
  timestamp: v.number(),
  metadata: v.optional(v.object({})), // Future extensibility
})
  .index('by_user_id', ['userId'])
  .index('by_user_activity_type', ['userId', 'activityType']);
```

**New Table: `userMilestoneProgress`**

```typescript
userMilestoneProgress: defineTable({
  userId: v.id('users'),
  milestoneId: v.string(),
  achievedAt: v.number(),
  pointsEarned: v.number(),
  badgesEarned: v.array(v.string()),
})
  .index('by_user_id', ['userId'])
  .index('by_milestone_id', ['milestoneId']);
```

**Schema Updates for User Table:**

- Ensure `points` field exists and is properly indexed
- Verify `tier` field for future tier progression features

---

## 5. Non-Functional Requirements

### 5.1 Usability & Design Requirements

**Simplicity:**

- Single-click action for primary use case
- No complex forms or multi-step processes
- Intuitive iconography and clear labeling
- Consistent with platform design system

**Accessibility:**

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Appropriate color contrast ratios
- Focus management for loading states

**Responsive Design:**

- Mobile-first approach (primary usage expected on mobile)
- Touch-friendly button sizing (minimum 44px)
- Readable typography across device sizes
- Consistent experience across desktop and mobile

### 5.2 Performance Requirements

**Response Time Targets:**

- Button click to loading state: < 100ms
- Database mutation completion: < 500ms
- UI updates after successful logging: < 200ms
- Total user feedback loop: < 1 second

**Loading State Management:**

- Immediate visual feedback upon user action
- Progressive loading for complex operations
- Graceful degradation for slow network conditions
- Timeout handling for failed requests (30-second timeout)

**Real-time Updates:**

- Convex subscription-based updates
- No polling required for data freshness
- Automatic UI synchronization across browser tabs
- Optimistic UI updates where appropriate

### 5.3 Security Requirements

**Authentication:**

- JWT token validation for all mutations
- Clerk-managed session verification
- Protection against token replay attacks
- Automatic token refresh handling

**Authorization:**

- User can only log activities for their own account
- Multi-tenant data isolation by client ID
- Rate limiting to prevent abuse (max 1 log per minute)
- Input validation and sanitization

**Data Integrity:**

- Atomic transactions for point updates
- Idempotency for duplicate request protection
- Audit trail for all activity logging
- Backup and recovery procedures

### 5.4 Data Handling Requirements

**Atomicity:**

- All related updates (activity, points, milestones) occur in single transaction
- Rollback capability for failed operations
- Consistent state maintenance across table updates

**Consistency:**

- Real-time point calculations
- Milestone achievement validation
- Cross-table referential integrity
- Eventual consistency for non-critical data

**Data Retention:**

- Activity logs retained indefinitely for user history
- Milestone achievements permanently recorded
- Point transaction audit trail maintained
- User preference for data export capability

---

## 6. Out of Scope

The following items are **intentionally excluded** from this initial implementation:

### 6.1 Additional Activity Types

- Workout duration tracking
- Specific exercise logging
- Nutrition tracking
- Sleep or wellness activities
- Social activities (group challenges, friend interactions)

### 6.2 Advanced Milestone Features

- Multi-condition milestones (e.g., "5 classes in 7 days")
- Time-based milestones (streaks, monthly goals)
- Social milestones (group achievements)
- Seasonal or event-based milestones

### 6.3 Activity Management Features

- Editing or deleting logged activities
- Bulk activity import
- Manual time/date entry for historical activities
- Activity categories or tags

### 6.4 Gamification Enhancements

- Leaderboards or competitive features
- Tier progression system activation
- Social sharing capabilities
- Reward redemption interface

### 6.5 Advanced UI Features

- Activity history dashboard
- Detailed progress analytics
- Calendar integration
- Notification preferences
- Custom dashboard widgets

### 6.6 Administrative Features

- Client admin activity management
- Bulk milestone configuration
- Advanced reporting and analytics
- User activity moderation tools

---

## 7. Success Metrics

### 7.1 Activity Log Rate

**Definition:** Percentage of authenticated users who successfully log at least one class attendance within their first week of account creation.

**Target:** 60% of new users log activity within 7 days
**Measurement Method:**

- Track user registration timestamps
- Monitor first activity log timestamps
- Calculate ratio of users with activity within 7-day window

**Technical Implementation:**

- Analytics event fired on successful `logClassAttendance` completion
- Dashboard tracking for new user engagement
- Weekly cohort analysis reporting

### 7.2 Milestone Achievement Rate

**Definition:** Percentage of users who successfully unlock the "First Steps at Studio A" milestone upon their first activity log.

**Target:** 100% success rate (no missed milestone triggers)
**Measurement Method:**

- Track all `class_attendance` activities for users with zero prior activities
- Verify corresponding milestone achievement records
- Alert on any missed milestone triggers

**Technical Implementation:**

- Automated testing of milestone evaluation logic
- Database consistency checks
- Real-time monitoring for milestone failures

### 7.3 Data Integrity

**Definition:** Accuracy and consistency of points, activities, and milestone data across all user interactions.

**Target:** 99.9% data accuracy with zero point discrepancies
**Measurement Method:**

- Automated point balance verification
- Cross-table referential integrity checks
- User-reported discrepancy tracking

**Technical Implementation:**

- Daily automated data reconciliation scripts
- Real-time point calculation validation
- Error logging and alerting for data inconsistencies

### 7.4 Real-time UI Update Performance

**Definition:** Speed and reliability of user interface updates following successful activity logging.

**Target:** 95% of UI updates complete within 1 second of mutation success
**Measurement Method:**

- Client-side performance monitoring
- Network request/response timing analysis
- User experience analytics (time to visual feedback)

**Technical Implementation:**

- Frontend performance measurement hooks
- Convex subscription latency monitoring
- User session replay for UX analysis

### 7.5 Error Rate and Recovery

**Definition:** Frequency of failed activity logging attempts and user recovery success rate.

**Target:** < 1% error rate with 90% user retry success
**Measurement Method:**

- Backend error logging and categorization
- Frontend error state tracking
- User retry attempt success monitoring

**Technical Implementation:**

- Comprehensive error tracking and alerting
- User error reporting mechanisms
- Automated error recovery testing

---

## 8. Open Questions / Assumptions

### 8.1 Validated Assumptions

**Database Seeding:**

- ✅ **Assumption:** The "First Class" milestone configuration is already seeded in the database through the existing seed script
- ✅ **Validation:** Confirmed in `packages/db/convex/seed.ts` with milestone ID `first-class-studio`

**Authentication Infrastructure:**

- ✅ **Assumption:** User authentication and session management via Clerk is fully operational
- **Validation:** Confirmed webhook integration and user synchronization in place

**Database Schema:**

- ✅ **Assumption:** Core user and client tables exist and are properly indexed
- **Validation:** Confirmed schema definition in `packages/db/convex/schema.ts`

### 8.2 Technical Architecture Questions

**MilestoneService Package Location:**

- **Question:** Should the `MilestoneService` be created in `packages/core` or `packages/shared`?
- **Recommendation:** Create `packages/core` for business logic separation
- **Impact:** Architecture decision affects future service organization

**Activity Points Configuration:**

- **Question:** Should base activity points be hardcoded or client-configurable?
- **Recommendation:** Start with hardcoded 10 points, make configurable in v2
- **Impact:** Affects client configuration flexibility

**Rate Limiting Implementation:**

- **Question:** Should rate limiting be implemented at the database or application level?
- **Recommendation:** Application-level with user ID-based tracking
- **Impact:** Prevents spam while maintaining performance

### 8.3 Business Logic Clarifications

**Multiple Class Types:**

- **Question:** Do different class types (yoga, HIIT, strength) earn different points?
- **Assumption:** All class types earn equal points for MVP
- **Impact:** Affects future activity type expansion

**Same-Day Activity Limits:**

- **Question:** Can users log multiple class attendances in a single day?
- **Assumption:** Yes, but with reasonable rate limiting (1 per minute)
- **Impact:** Affects user experience and data integrity

**Milestone Retroactivity:**

- **Question:** Should milestones apply retroactively to existing user activities?
- **Assumption:** No, milestones only apply to future activities
- **Impact:** Affects existing user experience and data migration

### 8.4 UX/UI Design Decisions

**Button Placement:**

- **Question:** Should the log button be persistent or contextual (e.g., location-based)?
- **Assumption:** Persistent on dashboard for MVP
- **Impact:** Affects user workflow and adoption

**Success Feedback:**

- **Question:** Should milestone achievements show as modal dialogs or toast notifications?
- **Recommendation:** Toast notifications to avoid interrupting user flow
- **Impact:** Affects user engagement and notification fatigue

**Mobile vs Desktop Priority:**

- **Question:** Which platform should receive primary design focus?
- **Assumption:** Mobile-first, as users likely log immediately after classes
- **Impact:** Affects responsive design implementation priority

### 8.5 Validation Required

**Client Configuration:**

- **Need to Confirm:** A given client's configuration includes proper milestone definitions
- **Validation Method:** Review seeded data and test milestone evaluation
- **Timeline:** Before development begins

**Performance Baselines:**

- **Need to Establish:** Current dashboard load times and mutation performance
- **Validation Method:** Baseline performance testing
- **Timeline:** During development setup

**User Testing:**

- **Need to Validate:** User flow intuition and button placement effectiveness
- **Validation Method:** Prototype testing with target persona representatives
- **Timeline:** Before final UI implementation

---

## 9. Dependencies & Timeline

### 9.1 Technical Dependencies

**Required Before Development:**

- [ ] `packages/core` package creation and setup
- [ ] Database schema migrations for `activities` and `userMilestoneProgress` tables
- [ ] Convex function structure setup in `packages/db/convex/functions`

**Parallel Development Tracks:**

- Frontend UI implementation (Dashboard button and states)
- Backend mutation development (`logClassAttendance`)
- Core business logic development (`MilestoneService`)

### 9.2 External Dependencies

**Third-Party Services:**

- Convex platform stability and performance
- Clerk authentication service availability
- Vercel hosting for frontend deployment

**Internal Dependencies:**

- Design system components for consistent UI
- Error logging and monitoring infrastructure
- Testing framework setup for automated validation

---

**End of Document**

_This PRD serves as the definitive guide for implementing the Log Class Attendance Activity & First Class Milestone feature. All development work should align with the requirements and success criteria outlined above._
