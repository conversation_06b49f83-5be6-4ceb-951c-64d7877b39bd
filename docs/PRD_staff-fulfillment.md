# PRD: In-Person Reward Fulfillment

**Author:** <PERSON> (Product Manager)
**Version:** 1.0
**Status:** In Review

---

## 1. Introduction

### 1.1. Overview

This document outlines the requirements for the "In-Person Reward Fulfillment" feature. Currently, members can redeem points for rewards digitally, but there is no standardized, secure process for front-desk staff to verify and hand over these rewards in person. This creates operational friction, risks inaccurate record-keeping, and can lead to a disjointed member experience.

The In-Person Reward Fulfillment feature will introduce a dedicated interface within the admin panel for authorized staff. This tool will allow them to quickly find a member, view their pending reward redemptions, and mark them as "Fulfilled" in real-time.

### 1.2. Product Goals & Strategy

This feature directly supports our core product strategy of providing a robust, end-to-end gamification and rewards platform for our clients. By bridging the gap between digital redemption and physical fulfillment, we:

- **Enhance Operational Efficiency:** Streamline front-desk operations, saving staff time and reducing manual tracking errors.
- **Improve Member Experience:** Provide a seamless and quick reward collection process for members.
- **Increase Trust and Security:** Ensure every fulfilled reward is accounted for with a clear and immutable audit trail, preventing fraud and inventory discrepancies.

---

## 2. Target Audience

The primary users of this feature are client employees with operational roles at the fitness facility.

- **Primary Roles:** `Staff`, `Admin`
- **User Persona: Maria, the Front-Desk Lead**
  - **Who she is:** Maria has been the front-desk lead at "The Handle Bar" for two years. She is the hub of the studio—greeting members, managing class check-ins, answering phone calls, and handling merchandise sales.
  - **Her Environment:** The front desk is a high-traffic, busy environment. Maria is constantly multitasking.
  - **Her Goals & Pains:** She wants to provide quick, cheerful, and efficient service to members. She gets frustrated by clunky software or processes that require multiple steps and clicks, as it creates a queue and pulls her attention away from the members in front of her. She is concerned about accurately tracking redeemed merchandise to keep inventory correct.
  - **Her Needs:** Maria needs a tool that is extremely fast, intuitive, and requires minimal training. She needs to be able to find a member and fulfill their reward in seconds, without having to write anything down manually.

---

## 3. User Stories

### Epic: Staff Manages In-Person Reward Fulfillment

| ID  | User Story                                                                                                                                                            | Role  | Priority  |
| --- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----- | --------- |
| 1   | As a front-desk lead, I want to quickly search for a member by their name or email so that I can pull up their account without making them wait.                      | Staff | Must-Have |
| 2   | As a staff member, I want to view a clear list of a specific member's _pending_ redemptions so I can confirm which reward they are claiming.                          | Staff | Must-Have |
| 3   | As a staff member, I want to mark a redemption as "Fulfilled" with a clear confirmation step, so that the system records the transaction accurately and in real-time. | Staff | Must-Have |
| 4   | As a staff member, I want to see a general list of all recent _pending_ redemptions across all members, so I can have an overview of outstanding items.               | Staff | High      |
| 5   | As a staff member, I want to view a member's _fulfilled_ redemption history, including who fulfilled it and when, so I can answer questions or resolve disputes.      | Staff | High      |
| 6   | As an admin, I want to ensure that only authorized `Staff` and `Admin` users can access the fulfillment interface, to maintain security and prevent fraud.            | Admin | Must-Have |
| 7   | As a staff member, I need to see clear loading/success feedback after marking an item as fulfilled, so I am confident the action was completed successfully.          | Staff | Must-Have |

---

## 4. Functional Requirements

### 4.1. Access Control & UI

- A new "Redemption Fulfillment" tab/link shall be added to the main navigation of the admin panel.
- This tab shall only be visible to and accessible by users with the role of `Staff` or `Admin`. Attempts by other users to access the URL directly should result in a "Permission Denied" error.

### 4.2. Member Search

- The interface will feature a prominent search bar.
- The search bar must support querying for users by their first name, last name, full name, or email address.
- Search will be powered by a backend search index to ensure fast and tolerant matching.
- As the staff member types, a list of matching users shall appear asynchronously below the search bar. Selecting a user will populate the redemptions list.

### 4.3. Redemptions List View

- The view will contain a list of `userRedemptions`. Each item in the list must display:
  - Member's Full Name
  - Reward Name (e.g., "Free Smoothie," "Branded T-Shirt")
  - Date of Redemption (when the member spent the points)
  - Status (`Pending` or `Fulfilled`)
- **Default View:** On load, the list will display all `Pending` redemptions for the entire client, sorted with the most recent redemptions first.
- **Filtered View:** Upon selecting a user from the search results, the list will update to show all redemptions (both `Pending` and `Fulfilled`) for that specific user, sorted by date.
- **Audit Information:** For redemptions with a `Fulfilled` status, the list must also display:
  - `Fulfilled At:` (Timestamp of fulfillment)
  - `Fulfilled By:` (Name of the staff member who fulfilled it)

### 4.4. Fulfillment Workflow

1.  For each redemption with a `Pending` status, a "Mark as Fulfilled" button will be displayed.
2.  Clicking this button will trigger a confirmation modal to prevent accidental actions. The modal will read: "Are you sure you want to fulfill this [Reward Name] for [Member Name]?" with "Cancel" and "Confirm" actions.
3.  Upon clicking "Confirm," the frontend will call the `fulfillRedemption` mutation.
4.  The system will show a loading state (e.g., spinner on the button) while the request is in flight.
5.  On successful completion:
    - The backend updates the `userRedemptions` record:
      - `status` is set to `Fulfilled`.
      - `fulfilledAt` is set to the current server timestamp.
      - `fulfilledBy` is set to the `userId` of the currently authenticated staff member.
    - The frontend will display a success notification (e.g., a toast message: "Reward fulfilled for [Member Name]!").
    - The item's status in the UI will immediately update to `Fulfilled`, and the "Mark as Fulfilled" button will be hidden/disabled.

---

## 5. Non-Functional Requirements

| Category               | Requirement                                                                                                                                                                                                                                                       |
| ---------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Usability & Design** | The interface must be extremely simple, intuitive, and require minimal clicks. It should be designed for a fast-paced, multitasking environment. The visual design should align with the existing admin panel and be clean and uncluttered.                       |
| **Performance**        | User search results and the loading/filtering of the redemption list must feel instantaneous to the user (target response time < 500ms).                                                                                                                          |
| **Security**           | Access to the feature must be strictly role-gated on both the frontend (UI elements hidden) and backend (API endpoints protected). All data transmission will be over HTTPS.                                                                                      |
| **Accessibility**      | The feature must comply with WCAG 2.1 Level AA standards, ensuring it is navigable via keyboard, compatible with screen readers, and has sufficient color contrast.                                                                                               |
| **Data & Audit**       | The audit trail (`fulfilledAt`, `fulfilledBy`) is critical. The system must guarantee that these fields are populated for every `Fulfilled` transaction. Once a record is marked as fulfilled, it should be considered immutable from a staff user's perspective. |

---

## 6. Out of Scope

The initial release of this feature will **not** include the following:

- Allowing members to cancel a pending redemption via their own dashboard.
- Batch fulfillment of multiple rewards for one or more members at a time.
- The ability for staff to edit or reverse a fulfillment action.
- Email or push notifications to members upon reward fulfillment.

---

## 7. Success Metrics

| Metric                               | How We'll Measure It                                                                                                                                                   | Target                                     |
| ------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------ |
| **Average time to fulfill a reward** | Conduct time-and-motion studies (or staff surveys) to measure the average time from member request to a successfully logged fulfillment, both before and after launch. | >50% decrease in average fulfillment time. |
| **Audit Trail Accuracy**             | A recurring backend job will run a daily query: `SELECT COUNT(*) FROM userRedemptions WHERE status = 'Fulfilled' AND (fulfilledBy IS NULL OR fulfilledAt IS NULL)`.    | The result of this query must always be 0. |
| **Staff Satisfaction**               | Conduct a qualitative survey with front-desk staff 2 weeks post-launch, asking them to rate the ease of use of the new system on a scale of 1-5.                       | Average rating of 4.5/5 or higher.         |

---

## 8. Open Questions & Assumptions

- **Assumption:** Staff will have a device with a stable internet connection at the front desk.
- **Assumption:** The list of all pending redemptions should be sorted with the most recent at the top by default.
- **Question:** What is the desired process if a staff member marks the wrong item as fulfilled by mistake? (Note: Reversal is out of scope for v1, but we need a defined support process).
- **Question:** How should staff names be displayed in the audit trail (e.g., "Maria S.", "Maria Sanchez")? For v1, we will plan to use `[FirstName] [LastNameInitial]`.
- **Question:** What is the anticipated daily volume of pending redemptions? This will inform any decisions around pagination for the default list view.
