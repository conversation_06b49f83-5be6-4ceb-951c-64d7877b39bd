/**
 * TierService - Core business logic for tier progression system
 *
 * This service handles tier calculation and advancement logic based on user points.
 * Implements the tier thresholds defined in the PRD: Bronze, Silver, Gold, Platinum, Diamond.
 */

export interface TierThreshold {
  tier: string;
  pointsRequired: number;
  color: string;
  icon: string;
}

export interface TierCalculationInput {
  currentPoints: number;
  currentTier: string;
}

export interface TierCalculationResult {
  newTier: string;
  hasAdvanced: boolean;
  pointsToNextTier: number;
  progressPercentage: number;
  tierThresholds: TierThreshold[];
}

export interface TierAdvancement {
  previousTier: string;
  newTier: string;
  pointsAtAdvancement: number;
  hasAdvanced: boolean;
}

/**
 * Tier definitions with thresholds, colors, and icons as per PRD
 */
export const TIER_THRESHOLDS: TierThreshold[] = [
  {
    tier: 'Bronze',
    pointsRequired: 0,
    color: '#CD7F32',
    icon: 'https://cdn.hugeicons.com/icons/medal-third-place-bulk-rounded.svg',
  },
  {
    tier: 'Silver',
    pointsRequired: 500,
    color: '#C0C0C0',
    icon: 'https://cdn.hugeicons.com/icons/medal-second-place-bulk-rounded.svg',
  },
  {
    tier: 'Gold',
    pointsRequired: 1500,
    color: '#FFD700',
    icon: 'https://cdn.hugeicons.com/icons/medal-first-place-bulk-rounded.svg',
  },
  {
    tier: 'Platinum',
    pointsRequired: 4000,
    color: '#E5E4E2',
    icon: 'https://cdn.hugeicons.com/icons/medal-01-bulk-rounded.svg',
  },
  {
    tier: 'Diamond',
    pointsRequired: 10000,
    color: '#B9F2FF',
    icon: 'https://cdn.hugeicons.com/icons/medal-02-bulk-rounded.svg',
  },
];

/**
 * Calculate user's tier based on current points
 * Pure function for testability and reliability
 * O(1) complexity with single-pass calculation
 *
 * @param input Current points and tier information
 * @returns Tier calculation result with advancement status
 */
export function calculateUserTier(
  input: TierCalculationInput
): TierCalculationResult {
  const { currentPoints, currentTier } = input;

  // Find current tier based on points (reverse order for highest eligible tier)
  let newTier = 'Bronze';
  for (let i = TIER_THRESHOLDS.length - 1; i >= 0; i--) {
    if (currentPoints >= TIER_THRESHOLDS[i].pointsRequired) {
      newTier = TIER_THRESHOLDS[i].tier;
      break;
    }
  }

  // Determine if user has advanced
  const hasAdvanced = newTier !== currentTier;

  // Calculate progress to next tier
  const currentTierIndex = TIER_THRESHOLDS.findIndex((t) => t.tier === newTier);
  const nextTierIndex = currentTierIndex + 1;

  let pointsToNextTier = 0;
  let progressPercentage = 100; // Default to 100% if at highest tier

  if (nextTierIndex < TIER_THRESHOLDS.length) {
    const currentTierThreshold =
      TIER_THRESHOLDS[currentTierIndex].pointsRequired;
    const nextTierThreshold = TIER_THRESHOLDS[nextTierIndex].pointsRequired;

    pointsToNextTier = nextTierThreshold - currentPoints;

    // Calculate progress percentage within current tier range
    const tierRange = nextTierThreshold - currentTierThreshold;
    const pointsInCurrentTier = currentPoints - currentTierThreshold;
    progressPercentage = Math.min(100, (pointsInCurrentTier / tierRange) * 100);
  }

  return {
    newTier,
    hasAdvanced,
    pointsToNextTier: Math.max(0, pointsToNextTier),
    progressPercentage: Math.max(0, progressPercentage),
    tierThresholds: TIER_THRESHOLDS,
  };
}

/**
 * Get tier information by tier name
 *
 * @param tierName Name of the tier
 * @returns Tier threshold information or null if not found
 */
export function getTierInfo(tierName: string): TierThreshold | null {
  return TIER_THRESHOLDS.find((t) => t.tier === tierName) || null;
}

/**
 * Get next tier information for a given tier
 *
 * @param currentTier Current tier name
 * @returns Next tier information or null if at highest tier
 */
export function getNextTierInfo(currentTier: string): TierThreshold | null {
  const currentIndex = TIER_THRESHOLDS.findIndex((t) => t.tier === currentTier);
  if (currentIndex === -1 || currentIndex === TIER_THRESHOLDS.length - 1) {
    return null;
  }
  return TIER_THRESHOLDS[currentIndex + 1];
}

/**
 * Calculate tier statistics for community context
 *
 * @param userTier Current user's tier
 * @param allUserTiers Array of all user tiers for comparison
 * @returns Statistics about tier distribution
 */
export function calculateTierStatistics(
  userTier: string,
  allUserTiers: string[]
): {
  userPercentile: number;
  tierDistribution: Record<string, number>;
} {
  const tierCounts: Record<string, number> = {};

  // Initialize counts for all tiers
  TIER_THRESHOLDS.forEach((tier) => {
    tierCounts[tier.tier] = 0;
  });

  // Count actual user distribution
  allUserTiers.forEach((tier) => {
    if (tierCounts[tier] !== undefined) {
      tierCounts[tier]++;
    }
  });

  // Calculate percentile (users at or below current tier)
  const userTierIndex = TIER_THRESHOLDS.findIndex((t) => t.tier === userTier);
  let usersAtOrBelow = 0;

  for (let i = 0; i <= userTierIndex; i++) {
    usersAtOrBelow += tierCounts[TIER_THRESHOLDS[i].tier];
  }

  const userPercentile =
    allUserTiers.length > 0 ? (usersAtOrBelow / allUserTiers.length) * 100 : 0;

  return {
    userPercentile: Math.round(userPercentile),
    tierDistribution: tierCounts,
  };
}

export default {
  calculateUserTier,
  getTierInfo,
  getNextTierInfo,
  calculateTierStatistics,
  TIER_THRESHOLDS,
};
