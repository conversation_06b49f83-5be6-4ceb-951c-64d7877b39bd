/**
 * MilestoneService - Core business logic for milestone evaluation
 *
 * This service handles the evaluation of user activities against milestone criteria
 * and determines when milestones are achieved.
 */

export interface Activity {
  _id: string;
  userId: string;
  clientId: string;
  activityType: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  triggerType: string;
  conditions: {
    activityTypeMatcher: string;
    countThreshold: number;
  };
  rewards: Array<{
    type: string;
    value: string | number;
  }>;
  isRepeatable: boolean;
  isEnabled: boolean;
}

export interface MilestoneEvaluationInput {
  userId: string;
  activityType: string;
  userActivities: Activity[];
  availableMilestones: Milestone[];
  achievedMilestones: string[];
}

export interface MilestoneEvaluationResult {
  newlyAchieved: Milestone[];
  pointsAwarded: number;
  badgesEarned: string[];
}

/**
 * Evaluates if a user has achieved any new milestones based on their activities
 *
 * @param input Evaluation input containing user activities and available milestones
 * @returns Result containing newly achieved milestones and rewards
 */
export function evaluateUserMilestones(
  input: MilestoneEvaluationInput
): MilestoneEvaluationResult {
  const {
    activityType,
    userActivities,
    availableMilestones,
    achievedMilestones,
  } = input;

  // Initialize result
  const result: MilestoneEvaluationResult = {
    newlyAchieved: [],
    pointsAwarded: 0,
    badgesEarned: [],
  };

  // Filter enabled milestones that match the current activity type
  const eligibleMilestones = availableMilestones.filter(
    (milestone) =>
      milestone.isEnabled &&
      milestone.conditions.activityTypeMatcher === activityType &&
      // Skip already achieved non-repeatable milestones
      (milestone.isRepeatable || !achievedMilestones.includes(milestone.id))
  );

  // Count activities by type
  const activityCounts: Record<string, number> = {};
  userActivities.forEach((activity) => {
    activityCounts[activity.activityType] =
      (activityCounts[activity.activityType] || 0) + 1;
  });

  // Evaluate each eligible milestone
  eligibleMilestones.forEach((milestone) => {
    const { activityTypeMatcher, countThreshold } = milestone.conditions;
    const activityCount = activityCounts[activityTypeMatcher] || 0;

    // Check if milestone conditions are met
    if (activityCount >= countThreshold) {
      result.newlyAchieved.push(milestone);

      // Process rewards
      milestone.rewards.forEach((reward) => {
        if (reward.type === 'points') {
          result.pointsAwarded += Number(reward.value);
        } else if (reward.type === 'badge') {
          result.badgesEarned.push(reward.value as string);
        }
      });
    }
  });

  return result;
}

export default {
  evaluateUserMilestones,
};
