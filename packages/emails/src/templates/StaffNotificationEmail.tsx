import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface StaffNotificationEmailProps {
  memberFullName: string;
  memberEmail: string;
  rewardName: string;
  redemptionTimestamp: string;
  clientName: string;
  fulfillmentUrl: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const StaffNotificationEmail = ({
  memberFullName,
  memberEmail,
  rewardName,
  redemptionTimestamp,
  clientName,
  fulfillmentUrl,
}: StaffNotificationEmailProps) => (
  <Html>
    <Head />
    <Preview>New Reward Redemption for {rewardName}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={heading}>New Reward Redemption</Heading>
        <Section style={body}>
          <Text style={paragraph}>
            A new reward has been redeemed and is ready for fulfillment.
          </Text>
          <Hr style={hr} />
          <Text style={infoSection}>
            <strong>Member:</strong> {memberFullName} ({memberEmail})
          </Text>
          <Text style={infoSection}>
            <strong>Reward:</strong> {rewardName}
          </Text>
          <Text style={infoSection}>
            <strong>Redeemed At:</strong> {redemptionTimestamp}
          </Text>
          <Hr style={hr} />
          <Button style={button} href={fulfillmentUrl}>
            Go to Fulfillment Page
          </Button>
        </Section>
        <Text style={footer}>
          This is an automated notification for {clientName} staff.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default StaffNotificationEmail;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  width: '580px',
};

const heading = {
  color: '#32325d',
  fontSize: '24px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '30px 0',
};

const body = {
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  padding: '24px',
  border: '1px solid #e6ebf1',
};

const paragraph = {
  color: '#525f7f',
  fontSize: '16px',
  lineHeight: '24px',
  textAlign: 'left' as const,
};

const infoSection = {
  ...paragraph,
  paddingLeft: '10px',
  borderLeft: '3px solid #6772e5',
  margin: '16px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const button = {
  backgroundColor: '#6772e5', // Indigo
  borderRadius: '5px',
  color: '#ffffff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '12px',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
};
