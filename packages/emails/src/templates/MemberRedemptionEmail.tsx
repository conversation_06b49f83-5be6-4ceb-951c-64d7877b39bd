import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface MemberRedemptionEmailProps {
  userFirstName: string;
  rewardName: string;
  pointsSpent: number;
  newPointBalance: number;
  clientName: string;
  clientLogoUrl: string;
  redemptionHistoryUrl: string;
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const MemberRedemptionEmail = ({
  userFirstName,
  rewardName,
  pointsSpent,
  newPointBalance,
  clientName,
  clientLogoUrl,
  redemptionHistoryUrl,
}: MemberRedemptionEmailProps) => (
  <Html>
    <Head />
    <Preview>Your Reward Redemption from {clientName}!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={clientLogoUrl}
          width="120"
          height="auto"
          alt={`${clientName} Logo`}
          style={logo}
        />
        <Heading style={heading}>You've Redeemed a Reward!</Heading>
        <Section style={body}>
          <Text style={paragraph}>Hi {userFirstName},</Text>
          <Text style={paragraph}>
            You've successfully redeemed{' '}
            <Text style={highlight}>{rewardName}</Text> for{' '}
            <Text style={highlight}>{pointsSpent} points</Text>.
          </Text>
          <Text style={paragraph}>
            Your new point balance is{' '}
            <Text style={highlight}>{newPointBalance}</Text>.
          </Text>
          <Hr style={hr} />
          <Text style={paragraph}>
            To claim your reward, please show this email to the front desk
            staff.
          </Text>
          <Button style={button} href={redemptionHistoryUrl}>
            View My Redemption History
          </Button>
        </Section>
        <Text style={footer}>
          Thank you for being a valued member of {clientName}.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default MemberRedemptionEmail;

const main = {
  backgroundColor: '#F5F2E8', // Creamy White
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  width: '580px',
};

const logo = {
  margin: '0 auto',
};

const heading = {
  color: '#2D2D2D', // Deep Charcoal
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: '30px 0',
};

const body = {
  backgroundColor: '#FFFFFF',
  borderRadius: '12px',
  padding: '24px',
};

const paragraph = {
  color: '#2D2D2D',
  fontSize: '16px',
  lineHeight: '24px',
  textAlign: 'left' as const,
};

const highlight = {
  ...paragraph,
  color: '#D4A574', // Golden Amber
  display: 'inline',
  fontWeight: 'bold',
};

const hr = {
  borderColor: '#E6D7C3', // Warm Beige
  margin: '20px 0',
};

const button = {
  backgroundColor: '#D4A574', // Golden Amber
  borderRadius: '8px',
  color: '#FFFFFF',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '12px',
};

const footer = {
  color: '#A8B5A0', // Soft Sage
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
};
