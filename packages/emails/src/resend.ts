import { Resend } from 'resend';
import { render } from '@react-email/render';
import { MemberRedemptionEmail } from './templates/MemberRedemptionEmail';
import { StaffNotificationEmail } from './templates/StaffNotificationEmail';
import React from 'react';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * @param to Recipient's email address
 * @param subject Email subject
 * @param body React element to render as email body
 */
async function sendEmail(
  from: string,
  to: string,
  subject: string,
  body: React.ReactElement
) {
  const { data, error } = await resend.emails.send({
    from,
    to,
    subject,
    html: render(body),
  });

  if (error) {
    // TODO: Add structured logging
    console.error(
      `Failed to send email to ${to} with subject "${subject}"`,
      error
    );
    throw new Error('Failed to send email');
  }

  return data;
}

type MemberRedemptionEmailProps = React.ComponentProps<
  typeof MemberRedemptionEmail
>;

export async function sendMemberRedemptionEmail(
  from: string,
  to: string,
  props: MemberRedemptionEmailProps
) {
  const subject = `Your Reward Redemption from ${props.clientName}!`;
  return sendEmail(
    from,
    to,
    subject,
    React.createElement(MemberRedemptionEmail, props)
  );
}

type StaffNotificationEmailProps = React.ComponentProps<
  typeof StaffNotificationEmail
>;

export async function sendStaffNotificationEmail(
  from: string,
  to: string,
  props: StaffNotificationEmailProps
) {
  const subject = `New Reward Redemption: ${props.rewardName}`;
  return sendEmail(
    from,
    to,
    subject,
    React.createElement(StaffNotificationEmail, props)
  );
}
