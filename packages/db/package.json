{"name": "@fitness-rewards/db", "private": true, "version": "0.1.0", "type": "module", "main": "./convex/_generated/api.js", "types": "./convex/_generated/api.d.ts", "exports": {".": "./convex/_generated/api.js", "./react": "convex/react"}, "scripts": {"dev": "convex dev", "dev:with-env": "convex dev --env-file=../../.env", "deploy": "convex deploy", "generate": "convex dev --once"}, "dependencies": {"@clerk/backend": "^1.1.0", "@fitness-rewards/core": "workspace:*", "@fitness-rewards/shared": "workspace:*", "convex": "^1.24.8", "convex-helpers": "^0.1.41", "dayjs": "^1.11.11", "resend": "^3.2.0", "svix": "^1.22.0", "zod": "^3.23.8"}, "devDependencies": {"typescript": "^5.4.5"}}