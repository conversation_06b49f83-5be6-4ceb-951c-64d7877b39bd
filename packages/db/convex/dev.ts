import { internalMutation, mutation } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';
import { TIER_THRESHOLDS } from './lib/TierService';
import { generateAnonymousName } from './lib/anonymousNames';

// This user ID is from your logs.
const DEV_USER_ID = 'jd746r6a069ycas01qtn2sks6x7hbr1w' as Id<'users'>;

const getPointsForTier = (tier: string) => {
  const tierInfo = TIER_THRESHOLDS.find((t) => t.tier === tier);
  // Give slightly more points than required to show progress within the tier
  const basePoints = tierInfo ? tierInfo.pointsRequired : 0;
  return basePoints + (tierInfo ? 150 : 0);
};

/**
 * DEV_ONLY: Sets the current user to a specific tier for UI testing.
 */
export const setTierForUser = internalMutation({
  args: {
    tier: v.string(),
  },
  handler: async (ctx, { tier }) => {
    const tierExists = TIER_THRESHOLDS.some((t) => t.tier === tier);
    if (!tierExists) {
      throw new Error(`Invalid tier: ${tier}`);
    }
    const points = getPointsForTier(tier);
    await ctx.db.patch(DEV_USER_ID, { tier, points });
    return { success: true };
  },
});

/**
 * DEV_ONLY: Resets the current user to their original Bronze tier state.
 */
export const resetTierForUser = internalMutation({
  handler: async (ctx) => {
    await ctx.db.patch(DEV_USER_ID, {
      tier: 'Bronze',
      points: 130, // Original point value from logs
    });
    return { success: true };
  },
});

/**
 * DEV_ONLY: Migrates rewards from clientConfiguration to the new rewards table.
 * This is a one-time operation.
 */
export const migrateRewards = internalMutation({
  handler: async (ctx) => {
    // Check if migration has already run for any client
    const anyReward = await ctx.db.query('rewards').first();
    if (anyReward) {
      console.log('Migration already appears to have run. Aborting.');
      return { message: 'Migration already run.' };
    }

    const clientConfigs = await ctx.db.query('clientConfiguration').collect();
    let migratedCount = 0;

    for (const config of clientConfigs) {
      if (config.rewards && Array.isArray(config.rewards)) {
        for (const reward of config.rewards) {
          await ctx.db.insert('rewards', {
            clientId: config.clientId,
            name: reward.name,
            cost: reward.cost,
            description: reward.description || '',
            imageUrl: reward.imageUrl || '',
            isActive: true, // Default to active
          });
          migratedCount++;
        }
      }
    }

    console.log(`Migration complete. Migrated ${migratedCount} rewards.`);
    return {
      message: `Migration complete. Migrated ${migratedCount} rewards.`,
    };
  },
});

/**
 * DEV_ONLY: One-time script to add a default 'user' role to any existing users who lack one.
 */
export const backfillUserRoles = internalMutation({
  handler: async (ctx) => {
    const usersWithoutRole = await ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('role'), undefined))
      .collect();

    if (usersWithoutRole.length === 0) {
      return { message: 'All users already have roles. Nothing to do.' };
    }

    let updatedCount = 0;
    for (const user of usersWithoutRole) {
      await ctx.db.patch(user._id, { role: 'user' });
      updatedCount++;
    }

    const message = `Successfully added 'user' role to ${updatedCount} users.`;
    console.log(message);
    return { message };
  },
});

/**
 * DEV_ONLY: One-time script to forcefully set the dev user's role to 'admin'.
 * This is a targeted fix for a failed migration.
 */
export const forceSetAdminRole = internalMutation({
  handler: async (ctx) => {
    await ctx.db.patch(DEV_USER_ID, { role: 'admin' });
    const message = `Successfully set role to 'admin' for user ${DEV_USER_ID}.`;
    console.log(message);
    return { message };
  },
});

/**
 * DEV_ONLY: Removes the now-deprecated `rewards` field from all clientConfiguration documents.
 */
export const removeRewardsFromClientConfig = internalMutation({
  handler: async (ctx) => {
    const clientConfigs = await ctx.db.query('clientConfiguration').collect();

    let cleanedCount = 0;
    for (const config of clientConfigs) {
      if ('rewards' in config && config.rewards !== undefined) {
        await ctx.db.patch(config._id, { rewards: undefined });
        cleanedCount++;
      }
    }

    const message = `Cleanup complete. Patched 'rewards' field to undefined in ${cleanedCount} configurations.`;
    console.log(message);
    return { message };
  },
});

/**
 * DEV_ONLY: One-time script to backfill the `searchText` field for all users.
 * This is necessary to enable full-text search for existing records.
 */
export const backfillSearchText = mutation({
  handler: async (ctx) => {
    const users = await ctx.db.query('users').collect();
    let updatedCount = 0;
    for (const user of users) {
      const searchText =
        `${user.firstName ?? ''} ${user.lastName ?? ''} ${user.email}`.toLowerCase();
      if (user.searchText !== searchText) {
        await ctx.db.patch(user._id, { searchText });
        updatedCount++;
      }
    }
    const message = `Successfully updated searchText for ${updatedCount} users.`;
    console.log(message);
    return { message };
  },
});

/**
 * DEV_ONLY: One-time script to backfill the `leaderboardSettings` for all users.
 * This is necessary to enable the anonymous leaderboard feature for existing users.
 */
export const backfillLeaderboardSettings = internalMutation({
  handler: async (ctx) => {
    let updatedCount = 0;
    let hasMore = true;
    let cursor = null;

    while (hasMore) {
      const paginationResult = await ctx.db
        .query('users')
        .withIndex('by_clerk_user_id')
        .paginate({ numItems: 100, cursor: cursor });

      const usersToUpdate = paginationResult.page.filter(
        (user) => user.leaderboardSettings === undefined
      );

      for (const user of usersToUpdate) {
        const anonymousName = generateAnonymousName();
        const avatarSeed = crypto.randomUUID();
        await ctx.db.patch(user._id, {
          leaderboardSettings: {
            showRealName: false,
            anonymousName,
            avatarSeed,
          },
        });
        updatedCount++;
      }

      cursor = paginationResult.continueCursor;
      hasMore = paginationResult.isDone === false;
    }

    const message = `Successfully backfilled leaderboardSettings for ${updatedCount} users.`;
    console.log(message);
    return { message };
  },
});
