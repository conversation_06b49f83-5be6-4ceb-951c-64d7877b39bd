import { v } from 'convex/values';
import {
  query,
  mutation,
  internalMutation,
  internalQuery,
} from '../_generated/server';
import { getUserByClerkId } from './users';
import { QueryCtx, MutationCtx } from '../_generated/server';

/**
 * Throws an error if the user is not an admin.
 * @param ctx The query or mutation context.
 */
const requireAdminRole = async (ctx: QueryCtx | MutationCtx) => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error('Not authenticated');
  }

  const user = await getUserByClerkId(ctx, identity.subject);
  if (!user || user.role !== 'admin') {
    throw new Error('You do not have permission to perform this action.');
  }
  return user;
};

export const getMilestonesForClient = query({
  handler: async (ctx) => {
    const adminUser = await requireAdminRole(ctx);
    if (!adminUser.clientId) {
      throw new Error('Admin user is not associated with a client.');
    }

    return await ctx.db
      .query('milestones')
      .withIndex('by_client_id', (q) => q.eq('clientId', adminUser.clientId!))
      .collect();
  },
});

export const getActivityTypesForClient = query({
  handler: async (ctx) => {
    const adminUser = await requireAdminRole(ctx);
    if (!adminUser.clientId) {
      throw new Error('Admin user is not associated with a client.');
    }

    return await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', adminUser.clientId!))
      .collect();
  },
});

export const createMilestone = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    iconUrl: v.optional(v.string()),
    triggerType: v.string(),
    activityTypeMatcher: v.string(),
    countThreshold: v.number(),
    rewardPoints: v.number(),
    isEnabled: v.boolean(),
    isRepeatable: v.boolean(),
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdminRole(ctx);

    if (!adminUser.clientId) {
      throw new Error('Admin user is not associated with a client.');
    }

    // Validation
    if (args.name.length < 3 || args.name.length > 100) {
      throw new Error('Name must be between 3 and 100 characters.');
    }
    if (args.description && args.description.length > 500) {
      throw new Error('Description cannot exceed 500 characters.');
    }
    if (args.countThreshold < 1 || args.countThreshold > 1_000_000) {
      throw new Error('Count threshold must be between 1 and 1,000,000.');
    }
    if (args.rewardPoints < 0 || args.rewardPoints > 1_000_000) {
      throw new Error('Reward points must be between 0 and 1,000,000.');
    }

    const milestoneId = await ctx.db.insert('milestones', {
      clientId: adminUser.clientId,
      name: args.name,
      description: args.description,
      iconUrl: args.iconUrl,
      triggerType: args.triggerType,
      conditions: {
        activityTypeMatcher: args.activityTypeMatcher,
        countThreshold: args.countThreshold,
      },
      rewards: [{ type: 'points', value: args.rewardPoints }],
      isEnabled: args.isEnabled,
      isRepeatable: args.isRepeatable,
      createdBy: adminUser._id,
      lastModifiedBy: adminUser._id,
    });

    return milestoneId;
  },
});

export const updateMilestone = mutation({
  args: {
    milestoneId: v.id('milestones'),
    name: v.string(),
    description: v.optional(v.string()),
    iconUrl: v.optional(v.string()),
    triggerType: v.string(),
    activityTypeMatcher: v.string(),
    countThreshold: v.number(),
    rewardPoints: v.number(),
    isEnabled: v.boolean(),
    isRepeatable: v.boolean(),
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdminRole(ctx);

    if (!adminUser.clientId) {
      throw new Error('Admin user is not associated with a client.');
    }

    const existingMilestone = await ctx.db.get(args.milestoneId);
    if (
      !existingMilestone ||
      existingMilestone.clientId !== adminUser.clientId
    ) {
      throw new Error(
        'Milestone not found or you do not have permission to edit it.'
      );
    }

    // Validation
    if (args.name.length < 3 || args.name.length > 100) {
      throw new Error('Name must be between 3 and 100 characters.');
    }
    if (args.description && args.description.length > 500) {
      throw new Error('Description cannot exceed 500 characters.');
    }
    if (args.countThreshold < 1 || args.countThreshold > 1_000_000) {
      throw new Error('Count threshold must be between 1 and 1,000,000.');
    }
    if (args.rewardPoints < 0 || args.rewardPoints > 1_000_000) {
      throw new Error('Reward points must be between 0 and 1,000,000.');
    }

    await ctx.db.patch(args.milestoneId, {
      name: args.name,
      description: args.description,
      iconUrl: args.iconUrl,
      triggerType: args.triggerType,
      conditions: {
        activityTypeMatcher: args.activityTypeMatcher,
        countThreshold: args.countThreshold,
      },
      rewards: [{ type: 'points', value: args.rewardPoints }],
      isEnabled: args.isEnabled,
      isRepeatable: args.isRepeatable,
      lastModifiedBy: adminUser._id,
    });
  },
});

export const deleteMilestone = mutation({
  args: {
    milestoneId: v.id('milestones'),
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdminRole(ctx);

    if (!adminUser.clientId) {
      throw new Error('Admin user is not associated with a client.');
    }

    const existingMilestone = await ctx.db.get(args.milestoneId);
    if (
      !existingMilestone ||
      existingMilestone.clientId !== adminUser.clientId
    ) {
      throw new Error(
        'Milestone not found or you do not have permission to delete it.'
      );
    }

    // Check if any user has made progress on this milestone
    const progress = await ctx.db
      .query('userActivityCounts')
      .withIndex('by_activity_type', (q) =>
        q.eq(
          'activityTypeKey',
          existingMilestone.conditions.activityTypeMatcher
        )
      )
      .filter((q) => q.gt(q.field('count'), 0))
      .first();

    if (progress) {
      // Soft delete: disable the milestone if users have progress
      await ctx.db.patch(args.milestoneId, { isEnabled: false });
      return { wasSoftDeleted: true };
    } else {
      // Hard delete: remove the milestone if no users have progress
      await ctx.db.delete(args.milestoneId);
      return { wasSoftDeleted: false };
    }
  },
});

// Placeholder for future functions
