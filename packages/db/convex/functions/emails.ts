import { internal } from '../_generated/api';
import { internalAction } from '../_generated/server';
import { v } from 'convex/values';

/**
 * Internal action to send redemption notification emails to both the member and staff.
 * This action is triggered by the `rewards.redeem` mutation.
 *
 * For now, this function logs the email data that would be sent.
 * In a production environment, you would integrate with an email service like Resend.
 */
export const sendRedemptionEmails = internalAction({
  args: {
    userId: v.id('users'),
    rewardName: v.string(),
    pointsSpent: v.number(),
    newPoints: v.number(),
    clientId: v.id('clients'),
  },
  handler: async (
    ctx,
    { userId, rewardName, pointsSpent, newPoints, clientId }
  ) => {
    // 1. Fetch user and client details
    const user = await ctx.runQuery(internal.functions.users.get, {
      id: userId,
    });
    const client = await ctx.runQuery(internal.functions.users.getClient, {
      clientId,
    });
    const clientConfig = await ctx.runQuery(
      internal.functions.clientConfigs.getClientConfig,
      { clientId }
    );

    if (!user || !client) {
      console.error(`Could not find user ${userId} or client ${clientId}.`);
      return;
    }

    const fromEmail = clientConfig?.fromEmail;
    if (!fromEmail) {
      console.error(
        `Email 'fromAddress' is not configured for client ${clientId}. Skipping email notifications.`
      );
      return;
    }

    const appUrl = clientConfig?.appUrl || 'http://localhost:3000';

    // 2. Log Member Confirmation Email Data
    console.log('📧 Would send member redemption email:', {
      to: user.email,
      from: fromEmail,
      subject: `Your Reward Redemption from ${client.name}!`,
      data: {
        userFirstName: user.firstName || 'Member',
        rewardName,
        pointsSpent,
        newPointBalance: newPoints,
        clientName: client.name,
        clientLogoUrl: clientConfig?.branding.logoUrl || '',
        redemptionHistoryUrl: `${appUrl}/history`,
      },
    });

    // 3. Log Staff Notification Email Data (if configured)
    if (clientConfig?.notificationEmail) {
      console.log('📧 Would send staff notification email:', {
        to: clientConfig.notificationEmail,
        from: fromEmail,
        subject: `New Reward Redemption: ${rewardName}`,
        data: {
          memberFullName:
            `${user.firstName || ''} ${user.lastName || ''}`.trim(),
          memberEmail: user.email,
          rewardName,
          redemptionTimestamp: new Date().toLocaleString(),
          clientName: client.name,
          fulfillmentUrl: `${appUrl}/admin?tab=redemptions`,
        },
      });
    }

    // TODO: Replace console.log with actual email sending when ready
    // This could be done with Resend, SendGrid, or another email service
    console.log('✅ Email notifications logged successfully');
  },
});
