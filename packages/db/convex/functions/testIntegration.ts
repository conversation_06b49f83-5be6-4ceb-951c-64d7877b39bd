/**
 * Test functions for integration development and validation
 * These functions help test the integration flow without real API calls
 */

import { internalAction, internalMutation } from '../_generated/server';
import { v } from 'convex/values';
import { internal } from '../_generated/api';

/**
 * Test function to simulate Mariana-Tek check-in events
 * Useful for development and testing without real API access
 */
export const simulateMarianaTekCheckIn = internalAction({
  args: {
    clientId: v.id('clients'),
    userEmail: v.string(),
    externalUserId: v.string(),
    classType: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const mockEvent = {
      reservationId: `test_reservation_${Date.now()}`,
      userId: args.externalUserId,
      userEmail: args.userEmail,
      userName: 'Test User',
      classType: args.classType || 'Yoga Class',
      checkedInAt: new Date().toISOString(),
      externalTimestamp: Date.now(),
    };

    console.log('Simulating Mariana-Tek check-in event:', mockEvent);

    // Process the simulated event
    const result = await ctx.runAction(internal.functions.marianaTekSync.processCheckInEvent, {
      clientId: args.clientId,
      event: mockEvent,
    });

    return {
      mockEvent,
      result,
    };
  },
});

/**
 * Test function to create sample unmatched events for testing the admin UI
 */
export const createSampleUnmatchedEvents = internalMutation({
  args: {
    clientId: v.id('clients'),
    count: v.optional(v.number()),
  },
  async handler(ctx, args) {
    const count = args.count || 3;
    const sampleEvents = [];

    for (let i = 0; i < count; i++) {
      const eventId = await ctx.db.insert('unmatchedEvents', {
        clientId: args.clientId,
        provider: 'marianaTek',
        externalUserId: `test_user_${i + 1}`,
        externalUserEmail: `testuser${i + 1}@example.com`,
        externalUserName: `Test User ${i + 1}`,
        eventType: 'check_in',
        eventData: {
          reservationId: `test_reservation_${i + 1}`,
          classType: 'Sample Yoga Class',
          checkedInAt: new Date().toISOString(),
          externalTimestamp: Date.now() - (i * 60000), // Stagger times
        },
        receivedAt: Date.now() - (i * 60000),
        status: 'pending',
      });

      sampleEvents.push(eventId);
    }

    return {
      created: sampleEvents.length,
      eventIds: sampleEvents,
    };
  },
});

/**
 * Test function to validate the complete integration flow
 */
export const testIntegrationFlow = internalAction({
  args: {
    clientId: v.id('clients'),
    testUserEmail: v.string(),
  },
  async handler(ctx, args) {
    const results = {
      steps: [] as Array<{ step: string; success: boolean; details?: any; error?: string }>,
    };

    try {
      // Step 1: Test API credentials lookup
      results.steps.push({ step: 'API Credentials Lookup', success: true });
      
      const credentials = await ctx.runQuery(internal.functions.integrations.getApiCredentials, {
        clientId: args.clientId,
        provider: 'marianaTek',
      });

      if (!credentials) {
        results.steps.push({ 
          step: 'API Credentials Check', 
          success: false, 
          error: 'No credentials configured' 
        });
        return results;
      }

      results.steps.push({ 
        step: 'API Credentials Check', 
        success: true, 
        details: { hasCredentials: true, isActive: credentials.isActive }
      });

      // Step 2: Test user matching
      const userMatch = await ctx.runQuery(internal.functions.integrations.findUserByExternalId, {
        provider: 'marianaTek',
        externalUserId: 'test_user_123',
        email: args.testUserEmail,
        clientId: args.clientId,
      });

      results.steps.push({ 
        step: 'User Matching', 
        success: true, 
        details: { 
          matched: !!userMatch, 
          matchType: userMatch?.matchType 
        }
      });

      // Step 3: Test activity logging (if user found)
      if (userMatch) {
        try {
          const activityResult = await ctx.runMutation(internal.functions.activities.internalLogActivity, {
            userId: userMatch.user._id,
            activityTypeKey: 'class_attendance',
            source: 'api_poll',
            externalReference: {
              provider: 'marianaTek',
              externalId: 'test_reservation_123',
              externalTimestamp: Date.now(),
            },
          });

          results.steps.push({ 
            step: 'Activity Logging', 
            success: true, 
            details: { 
              pointsAwarded: activityResult.pointsAwarded,
              duplicate: activityResult.duplicate 
            }
          });
        } catch (error) {
          results.steps.push({ 
            step: 'Activity Logging', 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        // Step 4: Test unmatched event storage
        const unmatchedEventId = await ctx.runMutation(internal.functions.integrations.storeUnmatchedEvent, {
          clientId: args.clientId,
          provider: 'marianaTek',
          externalUserId: 'test_user_123',
          externalUserEmail: args.testUserEmail,
          externalUserName: 'Test User',
          eventType: 'check_in',
          eventData: {
            reservationId: 'test_reservation_123',
            classType: 'Test Class',
            checkedInAt: new Date().toISOString(),
            externalTimestamp: Date.now(),
          },
        });

        results.steps.push({ 
          step: 'Unmatched Event Storage', 
          success: true, 
          details: { eventId: unmatchedEventId }
        });
      }

      // Step 5: Test sync status update
      await ctx.runMutation(internal.functions.marianaTekSync.updateSyncStatus, {
        clientId: args.clientId,
        provider: 'marianaTek',
        status: 'success',
        eventsProcessed: 1,
        eventsMatched: userMatch ? 1 : 0,
        eventsUnmatched: userMatch ? 0 : 1,
      });

      results.steps.push({ 
        step: 'Sync Status Update', 
        success: true 
      });

    } catch (error) {
      results.steps.push({ 
        step: 'Integration Flow Test', 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    return results;
  },
});

/**
 * Clean up test data
 */
export const cleanupTestData = internalMutation({
  args: {
    clientId: v.id('clients'),
  },
  async handler(ctx, args) {
    // Remove test unmatched events
    const testEvents = await ctx.db
      .query('unmatchedEvents')
      .withIndex('by_client_and_status', (q) => 
        q.eq('clientId', args.clientId)
      )
      .filter((q) => q.eq(q.field('externalUserId'), 'test_user_123'))
      .collect();

    for (const event of testEvents) {
      await ctx.db.delete(event._id);
    }

    // Remove test activities
    const testActivities = await ctx.db
      .query('activities')
      .filter((q) => 
        q.and(
          q.eq(q.field('clientId'), args.clientId),
          q.eq(q.field('externalReference.externalId'), 'test_reservation_123')
        )
      )
      .collect();

    for (const activity of testActivities) {
      await ctx.db.delete(activity._id);
    }

    return {
      removedEvents: testEvents.length,
      removedActivities: testActivities.length,
    };
  },
});
