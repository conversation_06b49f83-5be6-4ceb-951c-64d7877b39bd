/**
 * Functions for managing unmatched events from external integrations
 * Allows admins to review and resolve events that couldn't be automatically matched to users
 */

import { mutation, query } from '../_generated/server';
import { internalMutation } from '../_generated/server';
import { v } from 'convex/values';
import { requireAdminUser } from '../lib/authHelpers';
import { internal } from '../_generated/api';

/**
 * Get unmatched events for admin review
 */
export const getUnmatchedEvents = query({
  args: {
    status: v.optional(v.union(v.literal('pending'), v.literal('resolved'), v.literal('ignored'))),
    limit: v.optional(v.number()),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);
    
    let query = ctx.db
      .query('unmatchedEvents')
      .withIndex('by_client_and_status', (q) => 
        q.eq('clientId', user.clientId!)
      );

    if (args.status) {
      query = query.filter((q) => q.eq(q.field('status'), args.status));
    }

    const events = await query
      .order('desc')
      .take(args.limit || 50);

    return events;
  },
});

/**
 * Get unmatched events summary for dashboard
 */
export const getUnmatchedEventsSummary = query({
  async handler(ctx) {
    const user = await requireAdminUser(ctx);
    
    const allEvents = await ctx.db
      .query('unmatchedEvents')
      .withIndex('by_client_and_status', (q) => 
        q.eq('clientId', user.clientId!)
      )
      .collect();

    const summary = {
      total: allEvents.length,
      pending: allEvents.filter(e => e.status === 'pending').length,
      resolved: allEvents.filter(e => e.status === 'resolved').length,
      ignored: allEvents.filter(e => e.status === 'ignored').length,
      byProvider: {} as Record<string, number>,
    };

    // Count by provider
    for (const event of allEvents) {
      summary.byProvider[event.provider] = (summary.byProvider[event.provider] || 0) + 1;
    }

    return summary;
  },
});

/**
 * Resolve an unmatched event by linking it to a user
 */
export const resolveUnmatchedEvent = mutation({
  args: {
    eventId: v.id('unmatchedEvents'),
    userId: v.id('users'),
    activityTypeKey: v.string(),
    notes: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);
    
    // Get the unmatched event
    const event = await ctx.db.get(args.eventId);
    if (!event || event.clientId !== user.clientId) {
      throw new Error('Event not found or access denied');
    }

    if (event.status !== 'pending') {
      throw new Error('Event has already been processed');
    }

    // Get the target user
    const targetUser = await ctx.db.get(args.userId);
    if (!targetUser || targetUser.clientId !== user.clientId) {
      throw new Error('User not found or access denied');
    }

    // Link external ID to user if not already linked
    await ctx.runMutation(internal.functions.integrations.linkExternalIdToUser, {
      userId: args.userId,
      provider: event.provider,
      externalUserId: event.externalUserId,
    });

    // Log the activity
    const activityResult = await ctx.runMutation(internal.functions.activities.internalLogActivity, {
      userId: args.userId,
      activityTypeKey: args.activityTypeKey,
      source: 'api_poll',
      externalReference: {
        provider: event.provider,
        externalId: event.eventData.reservationId || event.externalUserId,
        externalTimestamp: event.eventData.externalTimestamp || event.receivedAt,
      },
    });

    // Mark event as resolved
    await ctx.db.patch(args.eventId, {
      status: 'resolved',
      resolvedAt: Date.now(),
      resolvedBy: user._id,
      notes: args.notes,
    });

    return {
      success: true,
      pointsAwarded: activityResult.pointsAwarded,
      duplicate: activityResult.duplicate,
    };
  },
});

/**
 * Ignore an unmatched event (mark as not needing resolution)
 */
export const ignoreUnmatchedEvent = mutation({
  args: {
    eventId: v.id('unmatchedEvents'),
    notes: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);
    
    // Get the unmatched event
    const event = await ctx.db.get(args.eventId);
    if (!event || event.clientId !== user.clientId) {
      throw new Error('Event not found or access denied');
    }

    if (event.status !== 'pending') {
      throw new Error('Event has already been processed');
    }

    // Mark event as ignored
    await ctx.db.patch(args.eventId, {
      status: 'ignored',
      resolvedAt: Date.now(),
      resolvedBy: user._id,
      notes: args.notes,
    });

    return { success: true };
  },
});

/**
 * Bulk ignore multiple unmatched events
 */
export const bulkIgnoreUnmatchedEvents = mutation({
  args: {
    eventIds: v.array(v.id('unmatchedEvents')),
    notes: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);
    
    let processed = 0;
    let errors = 0;

    for (const eventId of args.eventIds) {
      try {
        const event = await ctx.db.get(eventId);
        if (event && event.clientId === user.clientId && event.status === 'pending') {
          await ctx.db.patch(eventId, {
            status: 'ignored',
            resolvedAt: Date.now(),
            resolvedBy: user._id,
            notes: args.notes,
          });
          processed++;
        } else {
          errors++;
        }
      } catch (error) {
        console.error(`Failed to ignore event ${eventId}:`, error);
        errors++;
      }
    }

    return { processed, errors };
  },
});

/**
 * Search users for matching unmatched events
 */
export const searchUsersForMatching = query({
  args: {
    searchTerm: v.string(),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);
    
    if (args.searchTerm.length < 2) {
      return [];
    }

    // Search by email or name
    const users = await ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('clientId'), user.clientId))
      .collect();

    const searchLower = args.searchTerm.toLowerCase();
    
    return users
      .filter(u => 
        u.email.toLowerCase().includes(searchLower) ||
        (u.firstName && u.firstName.toLowerCase().includes(searchLower)) ||
        (u.lastName && u.lastName.toLowerCase().includes(searchLower))
      )
      .slice(0, 10)
      .map(u => ({
        _id: u._id,
        email: u.email,
        firstName: u.firstName,
        lastName: u.lastName,
        displayName: u.firstName && u.lastName 
          ? `${u.firstName} ${u.lastName}` 
          : u.email,
      }));
  },
});

/**
 * Get activity types for resolving events
 */
export const getActivityTypesForResolution = query({
  async handler(ctx) {
    const user = await requireAdminUser(ctx);
    
    const activityTypes = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('isEnabled'), true))
      .collect();

    return activityTypes.map(at => ({
      key: at.key,
      name: at.name,
      points: at.points,
    }));
  },
});
