import { v } from 'convex/values';
import { internalMutation, query } from '../_generated/server';
import { mutation } from '../_generated/server';
import {
  getUsersByClientId,
  requireAuthenticatedUser,
} from '../lib/authHelpers';

/**
 * Retrieves the configuration for the currently authenticated user's client.
 * @returns The client configuration object or null if not found.
 */
export const getCurrentClientConfig = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) {
      return null;
    }
    return await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId))
      .first();
  },
});

/**
 * Retrieves the configuration for a specific client.
 * @param clientId The ID of the client.
 * @returns The client configuration object or null if not found.
 */
export const getClientConfig = query({
  args: { clientId: v.id('clients') },
  handler: async (ctx, { clientId }) => {
    // TODO: Add auth check to ensure user can access this client's config
    return await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientId))
      .first();
  },
});

/**
 * Updates the configuration for a specific client.
 * This mutation can be called from the client-side.
 * @param clientId The ID of the client to update.
 * @param config The partial configuration object to apply.
 */
export const updateConfig = mutation({
  args: {
    clientId: v.id('clients'),
    config: v.object({
      notificationEmail: v.optional(v.string()),
      appUrl: v.optional(v.string()),
      fromEmail: v.optional(v.string()),
      branding: v.optional(
        v.object({
          avatarColors: v.optional(v.array(v.string())),
        })
      ),
    }),
  },
  handler: async (ctx, { clientId, config }) => {
    // TODO: Add robust auth check to ensure user is an admin for this client
    const existingConfig = await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientId))
      .first();

    if (!existingConfig) {
      throw new Error(`Configuration for client ${clientId} not found.`);
    }

    const { branding, ...otherConfig } = config;

    const patchPayload: Partial<
      Omit<Doc<'clientConfiguration'>, 'branding'>
    > & {
      branding?: Partial<Doc<'clientConfiguration'>['branding']>;
    } = { ...otherConfig };

    if (branding) {
      patchPayload.branding = {
        ...existingConfig.branding,
        ...branding,
      };
    }

    return await ctx.db.patch(existingConfig._id, patchPayload);
  },
});
