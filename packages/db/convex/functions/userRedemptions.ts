import { v } from 'convex/values';
import { internal } from '../_generated/api';
import {
  DatabaseReader,
  DatabaseWriter,
  mutation,
  query,
} from '../_generated/server';
import {
  getAdmin,
  getAdminOrStaff,
  getAuthenticatedUser,
  requireAdminUser,
  requireAuthenticatedUser,
} from '../lib/authHelpers';
import { Doc } from '../_generated/dataModel';

/**
 * Fulfills a redemption request.
 *
 * Only accessible by users with 'admin' or 'staff' roles.
 * Updates the redemption status to 'Fulfilled' and records the fulfillment details.
 *
 * @param ctx - The context object.
 * @param redemptionId - The ID of the user redemption to fulfill.
 * @returns A promise that resolves when the operation is complete.
 */
export const fulfill = mutation({
  args: { redemptionId: v.id('userRedemptions') },
  handler: async (ctx, { redemptionId }) => {
    const staffMember = await requireAdminUser(ctx);

    await ctx.db.patch(redemptionId, {
      status: 'Fulfilled',
      fulfilledAt: Date.now(),
      fulfilledBy: staffMember._id,
    });
  },
});

/**
 * Retrieves all pending redemptions for the current client.
 *
 * Only accessible by users with 'admin' or 'staff' roles.
 *
 * @param ctx - The context object.
 * @returns A promise that resolves to an array of pending redemptions with associated reward and user details.
 */
export const listPending = query({
  handler: async (ctx) => {
    const user = await requireAdminUser(ctx);
    if (!user.clientId) throw new Error('User not associated with a client');
    const client = await ctx.db.get(user.clientId);
    if (!client) throw new Error('Client not found');

    const pendingRedemptions = await ctx.db
      .query('userRedemptions')
      .withIndex('by_status', (q) => q.eq('status', 'Pending'))
      .filter((q) => q.eq(q.field('clientId'), client._id))
      .order('desc')
      .collect();

    return Promise.all(
      pendingRedemptions.map(async (redemption) => {
        const reward = await ctx.db.get(redemption.rewardId);
        const user = await ctx.db.get(redemption.userId);
        const fulfilledBy = redemption.fulfilledBy
          ? await ctx.db.get(redemption.fulfilledBy)
          : null;
        return {
          ...redemption,
          rewardName: reward?.name ?? 'Unknown Reward',
          userName: user
            ? `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim()
            : 'Unknown User',
          fulfilledByName: fulfilledBy
            ? `${fulfilledBy.firstName ?? ''} ${
                fulfilledBy.lastName ?? ''
              }`.trim()
            : null,
        };
      })
    );
  },
});

/**
 * Retrieves all redemptions for a specific user.
 *
 * Only accessible by users with 'admin' or 'staff' roles.
 *
 * @param ctx - The context object.
 * @param userId - The ID of the user whose redemptions are to be retrieved.
 * @returns A promise that resolves to an array of the user's redemptions with associated reward details.
 */
export const listForUser = query({
  args: { userId: v.id('users') },
  handler: async (ctx, { userId }) => {
    await requireAdminUser(ctx);

    const userRedemptions = await ctx.db
      .query('userRedemptions')
      .withIndex('by_user_id', (q) => q.eq('userId', userId))
      .order('desc')
      .collect();

    return Promise.all(
      userRedemptions.map(async (redemption) => {
        const reward = await ctx.db.get(redemption.rewardId);
        const user = await ctx.db.get(redemption.userId);
        const fulfilledBy = redemption.fulfilledBy
          ? await ctx.db.get(redemption.fulfilledBy)
          : null;
        return {
          ...redemption,
          rewardName: reward?.name ?? 'Unknown Reward',
          userName: user
            ? `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim()
            : 'Unknown User',
          fulfilledByName: fulfilledBy
            ? `${fulfilledBy.firstName ?? ''} ${
                fulfilledBy.lastName ?? ''
              }`.trim()
            : null,
        };
      })
    );
  },
});

/**
 * Searches for users by name or email.
 *
 * This query uses a search index for efficient searching.
 * Only accessible by authenticated users.
 *
 * @param ctx - The context object.
 * @param search - The search string.
 * @returns A promise that resolves to an array of users matching the search criteria.
 */
export const searchUsers = query({
  args: { search: v.string() },
  handler: async (ctx, { search }) => {
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) return [];

    if (!search) {
      return [];
    }

    const users = await ctx.db
      .query('users')
      .withSearchIndex('by_searchText', (q) =>
        q.search('searchText', search).eq('clientId', user.clientId)
      )
      .take(10); // Limit results for performance

    return users;
  },
});

/**
 * Retrieves the redemption history for the currently authenticated user.
 *
 * @param ctx - The context object.
 * @returns A promise that resolves to an array of the user's past redemptions.
 */
export const getHistory = query({
  handler: async (ctx) => {
    const user = await requireAuthenticatedUser(ctx);
    return ctx.db
      .query('userRedemptions')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .order('desc')
      .collect();
  },
});
