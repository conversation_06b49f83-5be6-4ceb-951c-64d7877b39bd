import { v } from 'convex/values';
import {
  internalQuery,
  query,
  mutation,
  internalMutation,
} from '../_generated/server';
import {
  requireAuthenticatedUser,
  requireAdminUser,
  requireUserWithRole,
} from '../lib/authHelpers';
import {
  activityTypeCreateSchema,
  activityTypeUpdateSchema,
} from '@fitness-rewards/shared/schemas/activityTypeSchemas';
import { zodToConvex } from 'convex-helpers/server/zod';
import { Doc, Id } from '../_generated/dataModel';

/**
 * Get all activity types for the current admin's client.
 * Requires admin role.
 */
export const getForClient = query({
  async handler(ctx) {
    const admin = await requireAdminUser(ctx);
    if (!admin.clientId) {
      throw new Error('User does not belong to a client');
    }

    return ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', admin.clientId!))
      .collect();
  },
});

/**
 * Get all activity types for the current user's client.
 * For use by end-users.
 */
export const getForUser = query({
  async handler(ctx) {
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) {
      throw new Error('User does not belong to a client');
    }
    return ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .collect();
  },
});

/**
 * Creates a new activity type for a client.
 * Requires admin role.
 */
export const create = mutation({
  args: zodToConvex(activityTypeCreateSchema),
  async handler(ctx, args) {
    const admin = await requireAdminUser(ctx);

    // Check for key uniqueness within the client
    const existingActivityType = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', admin.clientId!))
      .filter((q) => q.eq(q.field('key'), args.key))
      .first();

    if (existingActivityType) {
      throw new Error(
        `Activity type with key "${args.key}" already exists for this client.`
      );
    }

    return await ctx.db.insert('activityTypes', {
      clientId: admin.clientId!,
      name: args.name,
      key: args.key,
      points: args.points,
      iconUrl: args.iconName, // Assuming iconName is the full URL or identifier
    });
  },
});

/**
 * Updates an existing activity type for a client.
 * Requires admin role.
 */
export const update = mutation({
  args: zodToConvex(activityTypeUpdateSchema),
  async handler(ctx, args) {
    await requireAdminUser(ctx);
    const { id, ...rest } = args;

    const existing = await ctx.db.get(id as any);
    if (!existing) {
      throw new Error('Activity type not found');
    }

    return await ctx.db.patch(existing._id, {
      name: rest.name,
      key: rest.key,
      points: rest.points,
      iconUrl: rest.iconName,
    });
  },
});

/**
 * Deletes an activity type for a client.
 * Requires admin role.
 * Prevents deletion if the activity type is used in any milestones.
 */
export const remove = mutation({
  args: { id: v.id('activityTypes') },
  async handler(ctx, { id }) {
    const admin = await requireAdminUser(ctx);

    const activityTypeToDelete = await ctx.db.get(id);
    if (
      !activityTypeToDelete ||
      activityTypeToDelete.clientId !== admin.clientId
    ) {
      throw new Error(
        'Activity type not found or you do not have permission to delete it.'
      );
    }

    // Data Integrity Check: Ensure this activity type is not used by any milestones
    const milestonesUsingActivityType = await ctx.db
      .query('milestones')
      .withIndex('by_client_id', (q) => q.eq('clientId', admin.clientId!))
      .filter((q) =>
        q.eq(
          q.field('conditions.activityTypeMatcher'),
          activityTypeToDelete.key
        )
      )
      .collect();

    if (milestonesUsingActivityType.length > 0) {
      throw new Error(
        'This activity type cannot be deleted because it is currently used by one or more milestones.'
      );
    }

    await ctx.db.delete(id);
    return { success: true };
  },
});

export const listActivityTypes = query({
  async handler(ctx): Promise<Doc<'activityTypes'>[]> {
    const admin = await requireAdminUser(ctx);
    const activityTypes = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', admin.clientId!))
      .collect();
    return activityTypes;
  },
});
