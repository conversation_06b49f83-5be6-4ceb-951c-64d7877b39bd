import { query } from '../_generated/server';
import { paginationOptsValidator } from 'convex/server';
import { requireAuthenticatedUser } from '../lib/authHelpers';
import { Doc } from '../_generated/dataModel';

/**
 * Fetches a paginated history of all user point-earning events.
 * This includes class attendance and milestone achievements.
 */
export const getActivityHistory = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const user = await requireAuthenticatedUser(ctx);

    return await ctx.db
      .query('userMilestoneProgress')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .order('desc') // Sort by _creationTime descending
      .paginate(args.paginationOpts);
  },
});

/**
 * Fetches all of a user's unique, unlocked achievements (milestones).
 * Filters out repeatable milestones and simple activities like class attendance.
 */
export const getAchievementsHistory = query({
  handler: async (ctx) => {
    const user = await requireAuthenticatedUser(ctx);

    const allProgress = await ctx.db
      .query('userMilestoneProgress')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .order('desc')
      .collect();

    // Filter out the synthetic "Class Attendance" records.
    // And only get unique milestones if they are not repeatable.
    const uniqueAchievements = new Map<string, Doc<'userMilestoneProgress'>>();

    for (const progress of allProgress) {
      // A "real" milestoneId won't contain '-progress'.
      const isRealMilestone = !progress.milestoneId.includes('-progress');

      if (isRealMilestone) {
        if (!uniqueAchievements.has(progress.milestoneId)) {
          uniqueAchievements.set(progress.milestoneId, progress);
        }
      }
    }

    return Array.from(
      uniqueAchievements.values()
    ) as Doc<'userMilestoneProgress'>[];
  },
});
