/**
 * Integration functions for automated activity logging
 * Handles API credentials, user matching, and webhook processing
 */

import { internalMutation, internalQuery, internalAction } from '../_generated/server';
import { mutation, query } from '../_generated/server';
import { v } from 'convex/values';
import { requireAdminUser } from '../lib/authHelpers';
import { internal } from '../_generated/api';
import { Id } from '../_generated/dataModel';

/**
 * Save encrypted API credentials for a client
 */
export const saveApiCredentials = internalMutation({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
    encryptedApiKey: v.string(),
    encryptedWebhookSecret: v.optional(v.string()),
  },
  async handler(ctx, args) {
    // Check if credentials already exist
    const existing = await ctx.db
      .query('clientApiCredentials')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', args.clientId).eq('provider', args.provider)
      )
      .first();

    const now = Date.now();

    if (existing) {
      // Update existing credentials
      await ctx.db.patch(existing._id, {
        encryptedApiKey: args.encryptedApiKey,
        encryptedWebhookSecret: args.encryptedWebhookSecret,
        updatedAt: now,
        isActive: true,
      });
      return existing._id;
    } else {
      // Create new credentials
      return await ctx.db.insert('clientApiCredentials', {
        clientId: args.clientId,
        provider: args.provider,
        encryptedApiKey: args.encryptedApiKey,
        encryptedWebhookSecret: args.encryptedWebhookSecret,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

/**
 * Test API connection for a client's integration
 */
export const testApiConnection = internalAction({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
  },
  async handler(ctx, args) {
    // Get credentials
    const credentials = await ctx.runQuery(internal.functions.integrations.getApiCredentials, {
      clientId: args.clientId,
      provider: args.provider,
    });

    if (!credentials) {
      return { success: false, error: 'No credentials found' };
    }

    // Test connection based on provider
    if (args.provider === 'marianaTek') {
      const { MarianaTekService } = await import('../lib/MarianaTekService');
      const service = new MarianaTekService(credentials.encryptedApiKey);
      const result = await service.testConnection();
      
      // Update test status
      await ctx.runMutation(internal.functions.integrations.updateTestStatus, {
        credentialsId: credentials._id,
        success: result.success,
        error: result.error,
      });

      return result;
    }

    return { success: false, error: 'Unsupported provider' };
  },
});

/**
 * Get API credentials for a client and provider
 */
export const getApiCredentials = internalQuery({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
  },
  async handler(ctx, args) {
    return await ctx.db
      .query('clientApiCredentials')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', args.clientId).eq('provider', args.provider)
      )
      .filter((q) => q.eq(q.field('isActive'), true))
      .first();
  },
});

/**
 * Update test status for API credentials
 */
export const updateTestStatus = internalMutation({
  args: {
    credentialsId: v.id('clientApiCredentials'),
    success: v.boolean(),
    error: v.optional(v.string()),
  },
  async handler(ctx, args) {
    await ctx.db.patch(args.credentialsId, {
      lastTestAt: Date.now(),
      lastTestStatus: args.success ? 'success' : 'failed',
      updatedAt: Date.now(),
    });
  },
});

/**
 * Find user by external ID with fallback to email matching
 */
export const findUserByExternalId = internalQuery({
  args: {
    provider: v.string(),
    externalUserId: v.string(),
    email: v.optional(v.string()),
    clientId: v.id('clients'),
  },
  async handler(ctx, args) {
    // Primary lookup: Find user with matching external ID
    const users = await ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('clientId'), args.clientId))
      .collect();

    const userWithExternalId = users.find(user => 
      user.externalIds?.some(ext => 
        ext.provider === args.provider && ext.userId === args.externalUserId
      )
    );

    if (userWithExternalId) {
      return { user: userWithExternalId, matchType: 'external_id' as const };
    }

    // Fallback lookup: Find user by email
    if (args.email) {
      const userByEmail = await ctx.db
        .query('users')
        .withIndex('by_email', (q) => q.eq('email', args.email))
        .filter((q) => q.eq(q.field('clientId'), args.clientId))
        .first();

      if (userByEmail) {
        return { user: userByEmail, matchType: 'email' as const };
      }
    }

    return null;
  },
});

/**
 * Link external ID to an existing user
 */
export const linkExternalIdToUser = internalMutation({
  args: {
    userId: v.id('users'),
    provider: v.string(),
    externalUserId: v.string(),
  },
  async handler(ctx, args) {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error('User not found');
    }

    const existingExternalIds = user.externalIds || [];
    
    // Check if this external ID is already linked
    const alreadyLinked = existingExternalIds.some(ext => 
      ext.provider === args.provider && ext.userId === args.externalUserId
    );

    if (!alreadyLinked) {
      const updatedExternalIds = [
        ...existingExternalIds,
        { provider: args.provider, userId: args.externalUserId }
      ];

      await ctx.db.patch(args.userId, {
        externalIds: updatedExternalIds,
      });
    }

    return user;
  },
});

/**
 * Store unmatched event for admin review
 */
export const storeUnmatchedEvent = internalMutation({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
    externalUserId: v.string(),
    externalUserEmail: v.optional(v.string()),
    externalUserName: v.optional(v.string()),
    eventType: v.string(),
    eventData: v.object({}),
  },
  async handler(ctx, args) {
    return await ctx.db.insert('unmatchedEvents', {
      clientId: args.clientId,
      provider: args.provider,
      externalUserId: args.externalUserId,
      externalUserEmail: args.externalUserEmail,
      externalUserName: args.externalUserName,
      eventType: args.eventType,
      eventData: args.eventData,
      receivedAt: Date.now(),
      status: 'pending',
    });
  },
});

/**
 * Get integration status for admin dashboard
 */
export const getIntegrationStatus = query({
  async handler(ctx) {
    const user = await requireAdminUser(ctx);
    
    const credentials = await ctx.db
      .query('clientApiCredentials')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', user.clientId!)
      )
      .collect();

    const syncStatuses = await ctx.db
      .query('integrationSyncStatus')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', user.clientId!)
      )
      .collect();

    return {
      credentials: credentials.map(cred => ({
        provider: cred.provider,
        isActive: cred.isActive,
        lastTestAt: cred.lastTestAt,
        lastTestStatus: cred.lastTestStatus,
      })),
      syncStatuses: syncStatuses.map(status => ({
        provider: status.provider,
        lastSyncAt: status.lastSyncAt,
        lastSyncStatus: status.lastSyncStatus,
        eventsProcessed: status.eventsProcessed,
        eventsMatched: status.eventsMatched,
        eventsUnmatched: status.eventsUnmatched,
      })),
    };
  },
});

/**
 * Admin function to configure integration
 */
export const configureIntegration = mutation({
  args: {
    provider: v.string(),
    apiKey: v.string(),
    webhookSecret: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const user = await requireAdminUser(ctx);

    // TODO: Encrypt the API key and webhook secret
    const encryptedApiKey = args.apiKey; // Placeholder - implement encryption
    const encryptedWebhookSecret = args.webhookSecret; // Placeholder - implement encryption

    // Save credentials
    const credentialsId = await ctx.runMutation(internal.functions.integrations.saveApiCredentials, {
      clientId: user.clientId!,
      provider: args.provider,
      encryptedApiKey,
      encryptedWebhookSecret,
    });

    // Test the connection
    const testResult = await ctx.runAction(internal.functions.integrations.testApiConnection, {
      clientId: user.clientId!,
      provider: args.provider,
    });

    return {
      credentialsId,
      testResult,
    };
  },
});
