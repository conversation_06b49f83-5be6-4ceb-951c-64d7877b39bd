import { v } from 'convex/values';
import { mutation, query } from '../_generated/server';
import { requireAdminUser, requireAuthenticatedUser } from '../lib/authHelpers';
import { Doc, Id } from '../_generated/dataModel';
import { internal } from '../_generated/api';

// --- Admin Mutations ---

/**
 * Creates a new reward for a client.
 * Only accessible by users with the 'admin' role.
 *
 * @param name - The name of the reward.
 * @param description - The description of the reward.
 * @param cost - The point cost of the reward.
 * @param imageUrl - A URL for the reward's image or icon name.
 * @param isActive - Whether the reward is active.
 * @returns The ID of the newly created reward.
 */
export const create = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    cost: v.number(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdminUser(ctx);
    if (!admin.clientId) {
      throw new Error('Admin is not associated with a client.');
    }

    const rewardId = await ctx.db.insert('rewards', {
      clientId: admin.clientId,
      ...args,
    });
    return rewardId;
  },
});

/**
 * Updates an existing reward.
 * Only accessible by users with the 'admin' role.
 *
 * @param rewardId - The ID of the reward to update.
 * @param name - The new name of the reward.
 * @param description - The new description.
 * @param cost - The new point cost.
 * @param imageUrl - The new image/icon URL.
 * @param isActive - The new active status.
 */
export const update = mutation({
  args: {
    rewardId: v.id('rewards'),
    name: v.string(),
    description: v.optional(v.string()),
    cost: v.number(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, { rewardId, ...rest }) => {
    await requireAdminUser(ctx);
    await ctx.db.patch(rewardId, rest);
  },
});

/**
 * Deletes a reward.
 * Only accessible by users with the 'admin' role.
 *
 * @param rewardId - The ID of the reward to delete.
 */
export const del = mutation({
  args: { rewardId: v.id('rewards') },
  handler: async (ctx, { rewardId }) => {
    await requireAdminUser(ctx);
    await ctx.db.delete(rewardId);
  },
});

// --- Admin Queries ---

/**
 * Retrieves all rewards for the admin's client.
 * Only accessible by users with 'admin' role.
 */
export const list = query({
  handler: async (ctx) => {
    const admin = await requireAdminUser(ctx);
    if (!admin.clientId) return [];
    return ctx.db
      .query('rewards')
      .withIndex('by_client_id', (q) => q.eq('clientId', admin.clientId!))
      .order('desc')
      .collect();
  },
});

// --- Member Queries & Mutations ---

/**
 * Retrieves all *active* rewards for the current user's client.
 * Accessible by any authenticated user.
 */
export const getActive = query({
  handler: async (ctx) => {
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) return [];
    const rewards = await ctx.db
      .query('rewards')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('isActive'), true))
      .order('asc', 'cost')
      .collect();
    return rewards;
  },
});

/**
 * Redeems a reward for the logged-in user.
 * This is an atomic transaction that deducts points and records the redemption.
 *
 * @param rewardId - The ID of the reward to redeem.
 * @returns An object with the success status and the user's new point total.
 */
export const redeem = mutation({
  args: {
    rewardId: v.id('rewards'),
  },
  handler: async (ctx, { rewardId }) => {
    const user = await requireAuthenticatedUser(ctx);
    const rewardToRedeem = await ctx.db.get(rewardId);

    if (!rewardToRedeem) {
      throw new Error('Reward not found.');
    }
    if (!rewardToRedeem.isActive) {
      throw new Error('This reward is not currently active.');
    }
    if (user.points < rewardToRedeem.cost) {
      throw new Error('Insufficient points.');
    }

    // Atomically patch user points and insert redemption record
    const newPoints = user.points - rewardToRedeem.cost;
    await ctx.db.patch(user._id, { points: newPoints });

    const redemptionId = await ctx.db.insert('userRedemptions', {
      userId: user._id,
      clientId: user.clientId!,
      rewardId: rewardToRedeem._id,
      rewardName: rewardToRedeem.name,
      pointsSpent: rewardToRedeem.cost,
      redemptionTimestamp: Date.now(),
      status: 'Pending', // All redemptions start as Pending for fulfillment
    });

    // After successful redemption, schedule email notifications
    await ctx.scheduler.runAfter(
      0,
      internal.functions.emails.sendRedemptionEmails,
      {
        userId: user._id,
        rewardName: rewardToRedeem.name,
        pointsSpent: rewardToRedeem.cost,
        newPoints,
        clientId: user.clientId!,
      }
    );

    return { success: true, newPoints };
  },
});
