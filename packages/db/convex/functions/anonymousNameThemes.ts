import { v } from 'convex/values';
import { mutation, query, internalQuery } from '../_generated/server';
import { requireAdminUser } from '../lib/authHelpers';
import { generateAnonymousName } from '../lib/anonymousNames';

/**
 * Lists all anonymous name themes for the current user's client.
 * @returns {Promise<Array<Doc<'anonymousNameTemplates'>>>} A list of themes.
 */
export const list = query({
  handler: async (ctx) => {
    const user = await requireAdminUser(ctx);
    if (!user.clientId) {
      return [];
    }
    return ctx.db
      .query('anonymousNameTemplates')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .collect();
  },
});

/**
 * Creates a new anonymous name theme for the admin's client.
 * @param {string} themeName - The name of the theme.
 * @param {string} adjectives - Comma-separated list of adjectives.
 * @param {string} nouns - Comma-separated list of nouns.
 * @returns {Promise<Id<'anonymousNameTemplates'>>} The ID of the new theme.
 */
export const create = mutation({
  args: {
    themeName: v.string(),
    adjectives: v.string(),
    nouns: v.string(),
  },
  handler: async (ctx, { themeName, adjectives, nouns }) => {
    const user = await requireAdminUser(ctx);
    if (!user.clientId) {
      throw new Error('Client ID not found for user.');
    }

    const adjectivesArray = adjectives
      .split(',')
      .map((s) => s.trim())
      .filter(Boolean);
    const nounsArray = nouns
      .split(',')
      .map((s) => s.trim())
      .filter(Boolean);

    if (adjectivesArray.length < 1 || nounsArray.length < 1) {
      throw new Error('Adjectives and nouns lists cannot be empty.');
    }

    return await ctx.db.insert('anonymousNameTemplates', {
      clientId: user.clientId,
      themeName,
      adjectives: adjectivesArray,
      nouns: nounsArray,
    });
  },
});

/**
 * Updates an existing anonymous name theme.
 * @param {Id<'anonymousNameTemplates'>} id - The ID of the theme to update.
 * @param {string} themeName - The new name for the theme.
 * @param {string} adjectives - The new comma-separated list of adjectives.
 * @param {string} nouns - The new comma-separated list of nouns.
 */
export const update = mutation({
  args: {
    id: v.id('anonymousNameTemplates'),
    themeName: v.string(),
    adjectives: v.string(),
    nouns: v.string(),
  },
  handler: async (ctx, { id, themeName, adjectives, nouns }) => {
    const user = await requireAdminUser(ctx);
    const theme = await ctx.db.get(id);

    if (!theme || theme.clientId !== user.clientId) {
      throw new Error(
        'Theme not found or you do not have permission to edit it.'
      );
    }

    const adjectivesArray = adjectives
      .split(',')
      .map((s) => s.trim())
      .filter(Boolean);
    const nounsArray = nouns
      .split(',')
      .map((s) => s.trim())
      .filter(Boolean);

    if (adjectivesArray.length < 1 || nounsArray.length < 1) {
      throw new Error('Adjectives and nouns lists cannot be empty.');
    }

    return await ctx.db.patch(id, {
      themeName,
      adjectives: adjectivesArray,
      nouns: nounsArray,
    });
  },
});

/**
 * Deletes an anonymous name theme.
 * @param {Id<'anonymousNameTemplates'>} id - The ID of the theme to delete.
 */
export const del = mutation({
  args: { id: v.id('anonymousNameTemplates') },
  handler: async (ctx, { id }) => {
    const user = await requireAdminUser(ctx);
    const theme = await ctx.db.get(id);

    if (!theme || theme.clientId !== user.clientId) {
      throw new Error(
        'Theme not found or you do not have permission to delete it.'
      );
    }

    await ctx.db.delete(id);
  },
});

/**
 * Internal query to generate an anonymous name for a user, respecting client-specific themes.
 * @param {Id<'clients'>} clientId - The ID of the client.
 * @returns {Promise<string>} A randomly generated anonymous name.
 */
export const internalGetAnonymousNameForClient = internalQuery({
  args: { clientId: v.id('clients') },
  handler: async (ctx, { clientId }) => {
    const customThemes = await ctx.db
      .query('anonymousNameTemplates')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientId))
      .collect();

    if (customThemes.length > 0) {
      // Pick a random theme
      const theme =
        customThemes[Math.floor(Math.random() * customThemes.length)];
      return generateAnonymousName(theme.adjectives, theme.nouns);
    }

    // Fallback to default
    return generateAnonymousName();
  },
});
