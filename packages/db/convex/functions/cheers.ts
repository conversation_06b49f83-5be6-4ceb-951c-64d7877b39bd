import { v } from 'convex/values';
import { mutation, query } from '../_generated/server';
import { requireAuthenticatedUser } from '../lib/authHelpers';
import { Doc, Id } from '@db/types';

const CHEER_COOLDOWN_MS = 60 * 60 * 1000; // 1 hour

/**
 * Sends a "cheer" to another user.
 * Includes a 1-hour cooldown per user pair to prevent spam.
 *
 * @param {Id<'users'>} cheeredUserId - The ID of the user to cheer.
 * @returns {Promise<{success: boolean, newCheerCount?: number}>} The result of the operation.
 */
export const send = mutation({
  args: {
    cheeredUserId: v.id('users'),
  },
  handler: async (ctx, { cheeredUserId }) => {
    const cheerer = await requireAuthenticatedUser(ctx);

    if (cheerer._id === cheeredUserId) {
      throw new Error("You can't cheer yourself!");
    }

    const cheeredUser = await ctx.db.get(cheeredUserId);
    if (!cheeredUser || cheeredUser.clientId !== cheerer.clientId) {
      throw new Error('User not found or not in the same client.');
    }

    const now = Date.now();
    const oneHourAgo = now - CHEER_COOLDOWN_MS;

    const recentCheer = await ctx.db
      .query('cheers')
      .withIndex('by_cheerer_and_cheered', (q) =>
        q.eq('cheererId', cheerer._id).eq('cheeredId', cheeredUserId)
      )
      .filter((q) => q.gt(q.field('timestamp'), oneHourAgo))
      .first();

    if (recentCheer) {
      throw new Error('You can only cheer this person once per hour.');
    }

    await ctx.db.insert('cheers', {
      cheererId: cheerer._id,
      cheeredId: cheeredUserId,
      clientId: cheerer.clientId!,
      timestamp: now,
    });

    return { success: true };
  },
});

/**
 * Gets cheer data for a list of users on the leaderboard.
 *
 * @param {Array<Id<'users'>>} userIds - A list of user IDs to get cheer data for.
 * @returns {Promise<Record<Id<'users'>, { count: number, cheeredByCurrentUser: boolean }>>} An object mapping user IDs to their cheer data.
 */
export const get = query({
  args: {
    userIds: v.array(v.id('users')),
  },
  handler: async (ctx, { userIds }) => {
    const currentUser = await requireAuthenticatedUser(ctx);
    if (userIds.length === 0) {
      return {};
    }

    const cheerData: Record<
      Id<'users'>,
      { count: number; cheeredByCurrentUser: boolean }
    > = {};

    const oneHourAgo = Date.now() - CHEER_COOLDOWN_MS;

    // Initialize result object
    for (const userId of userIds) {
      cheerData[userId] = { count: 0, cheeredByCurrentUser: false };
    }

    // Get all cheers for the users on the board
    const allCheers = await Promise.all(
      userIds.map((userId) =>
        ctx.db
          .query('cheers')
          .withIndex('by_cheered_user', (q) => q.eq('cheeredId', userId))
          .collect()
      )
    );

    // Process the cheers
    allCheers.flat().forEach((cheer) => {
      if (cheerData[cheer.cheeredId]) {
        cheerData[cheer.cheeredId].count++;
        if (
          cheer.cheererId === currentUser._id &&
          cheer.timestamp > oneHourAgo
        ) {
          cheerData[cheer.cheeredId].cheeredByCurrentUser = true;
        }
      }
    });

    return cheerData;
  },
});
