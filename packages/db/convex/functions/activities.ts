import { internalMutation, internalQuery } from '../_generated/server';
import { v } from 'convex/values';
import { requireAuthenticatedUser } from '../lib/authHelpers';
import { evaluateAndUpdateUserTier } from '../lib/TierService';
import { Doc } from '../_generated/dataModel';
import { ConvexError } from 'convex/values';
import { mutation, query } from '../_generated/server';
import { getUserId } from '../lib/utils';
import { MilestoneService } from '@fitness-rewards/core';

/**
 * Gets recent activities for the authenticated user.
 * Now includes pointsAwarded.
 */
export const getRecentActivities = query({
  async handler(ctx) {
    const user = await requireAuthenticatedUser(ctx);

    // Get the user's recent activities, sorted by timestamp (newest first)
    const activities = await ctx.db
      .query('activities')
      .withIndex('by_userId_timestamp', (q) => q.eq('userId', user._id))
      .order('desc')
      .take(10);

    return activities;
  },
});

/**
 * Logs a specific activity for a user, awards points, and evaluates milestones and tiers.
 * This is a generic replacement for logClassAttendance.
 */
export const logActivity = mutation({
  args: { activityTypeKey: v.string() },
  async handler(ctx, { activityTypeKey }) {
    // 1. Get authenticated user and their client
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) {
      throw new Error(
        'User does not belong to a client and cannot log activities.'
      );
    }

    // 2. Look up the activity type to get its point value
    const activityType = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('key'), activityTypeKey))
      .first();

    if (!activityType) {
      throw new Error(
        `Activity type "${activityTypeKey}" not found for this client.`
      );
    }

    // 3. Record the raw activity, including points awarded
    await ctx.db.insert('activities', {
      userId: user._id,
      clientId: user.clientId,
      activityType: activityTypeKey,
      timestamp: Date.now(),
      pointsAwarded: activityType.points,
    });

    // 4. Update user's specific activity count
    const userActivityCount = await ctx.db
      .query('userActivityCounts')
      .withIndex('by_user_and_activity', (q) =>
        q.eq('userId', user._id).eq('activityTypeKey', activityTypeKey)
      )
      .unique();

    const newCount = (userActivityCount?.count || 0) + 1;

    if (userActivityCount) {
      await ctx.db.patch(userActivityCount._id, { count: newCount });
    } else {
      await ctx.db.insert('userActivityCounts', {
        userId: user._id,
        activityTypeKey: activityTypeKey,
        count: newCount,
      });
    }

    // 5. Evaluate milestones
    const activeMilestones = await ctx.db
      .query('milestones')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('isEnabled'), true))
      .filter((q) =>
        q.eq(q.field('conditions.activityTypeMatcher'), activityTypeKey)
      )
      .collect();

    const alreadyAchieved = await ctx.db
      .query('userMilestoneProgress')
      .withIndex('by_user_id', (q) => q.eq('userId', user._id))
      .collect();
    const achievedMilestoneIds = new Set(
      alreadyAchieved.map((a) => a.milestoneId)
    );

    let milestonePointsAwarded = 0;
    const newlyAchievedMilestones: Doc<'milestones'>[] = [];

    for (const milestone of activeMilestones) {
      if (newCount >= milestone.conditions.countThreshold) {
        if (
          !achievedMilestoneIds.has(milestone._id) ||
          milestone.isRepeatable
        ) {
          // For now, we only handle non-repeatable milestones.
          // TODO: Add logic for repeatable milestones (e.g., reset progress or allow multiple achievements)
          if (!achievedMilestoneIds.has(milestone._id)) {
            newlyAchievedMilestones.push(milestone);
            const pointsReward = milestone.rewards.find(
              (r) => r.type === 'points'
            );
            const points = pointsReward ? (pointsReward.value as number) : 0;
            milestonePointsAwarded += points;

            await ctx.db.insert('userMilestoneProgress', {
              userId: user._id,
              milestoneId: milestone._id,
              achievedAt: Date.now(),
              pointsEarned: points,
              badgesEarned: [],
              milestoneName: milestone.name,
              milestoneDescription: milestone.description,
              milestoneIconUrl: milestone.iconUrl,
              milestoneConditions: milestone.conditions,
              isRepeatable: milestone.isRepeatable,
            });
          }
        }
      }
    }

    // 6. Update user points and evaluate tier
    const totalPointsEarned = activityType.points + milestonePointsAwarded;
    const newUserPoints = user.points + totalPointsEarned;
    await ctx.db.patch(user._id, { points: newUserPoints });

    const tierEvaluation = await evaluateAndUpdateUserTier(
      ctx,
      user._id,
      newUserPoints,
      user.tier,
      user.clientId
    );

    // 7. Return comprehensive response
    return {
      success: true,
      pointsAwarded: totalPointsEarned,
      newMilestones: newlyAchievedMilestones.map((m) => ({
        name: m.name,
        points: m.rewards.find((r) => r.type === 'points')?.value || 0,
      })),
      tierAdvancement: tierEvaluation.hasAdvanced
        ? {
            previousTier: tierEvaluation.previousTier,
            newTier: tierEvaluation.newTier,
          }
        : null,
    };
  },
});
