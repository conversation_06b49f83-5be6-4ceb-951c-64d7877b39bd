// This file will contain all the backend functions for the Client Admin Dashboard.
// It will include functions for managing rewards, fulfilling redemptions, and searching for users.
// All functions will be protected by role-based access control.

import { v } from 'convex/values';
import { mutation, query, QueryCtx } from '../_generated/server';
import { getUserByClerkId } from './users';
import { Doc, Id } from '../_generated/dataModel';

// =================================================================
// Internal Helper Functions
// =================================================================

/**
 * Throws an error if the user is not an admin or staff.
 * @param ctx - The query or mutation context.
 */
const requireAdminOrStaffRole = async (
  ctx: QueryCtx,
  allowedRoles: string[] = ['admin', 'staff']
) => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error('Not authenticated');
  }

  const user = await getUserByClerkId(ctx, identity.subject);
  if (!user) {
    throw new Error('User not found in database');
  }

  const role = user.role; // Use the role from the database

  if (!role || !allowedRoles.includes(role)) {
    throw new Error(
      `Permission denied. Required: ${allowedRoles.join(' or ')}, Found: ${role}`
    );
  }

  if (!user.clientId) {
    throw new Error('User is not associated with a client.');
  }

  return { user, role };
};

// =================================================================
// Rewards Management
// =================================================================

/**
 * Get all rewards for the current client administrator's client.
 */
export const getRewardsForClient = query({
  handler: async (ctx) => {
    const { user } = await requireAdminOrStaffRole(ctx);
    return ctx.db
      .query('rewards')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .collect();
  },
});

/**
 * Create a new reward for the client.
 */
export const createReward = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    cost: v.number(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const { user } = await requireAdminOrStaffRole(ctx, ['admin']); // Only admins can create
    await ctx.db.insert('rewards', {
      clientId: user.clientId!,
      ...args,
    });
  },
});

/**
 * Update an existing reward.
 */
export const updateReward = mutation({
  args: {
    rewardId: v.id('rewards'),
    name: v.string(),
    description: v.optional(v.string()),
    cost: v.number(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const { user } = await requireAdminOrStaffRole(ctx, ['admin']); // Only admins can update

    // Verify the reward belongs to the admin's client
    const existingReward = await ctx.db.get(args.rewardId);
    if (!existingReward || existingReward.clientId !== user.clientId) {
      throw new Error('Reward not found or you do not have permission.');
    }

    const { rewardId, ...rest } = args;
    await ctx.db.patch(rewardId, rest);
  },
});

/**
 * Delete a reward.
 */
export const deleteReward = mutation({
  args: { rewardId: v.id('rewards') },
  handler: async (ctx, { rewardId }) => {
    const { user } = await requireAdminOrStaffRole(ctx, ['admin']); // Only admins can delete

    const existingReward = await ctx.db.get(rewardId);
    if (!existingReward || existingReward.clientId !== user.clientId) {
      throw new Error('Reward not found or you do not have permission.');
    }

    // TODO: Decide on cascading delete behavior for userRedemptions
    // For now, we'll just delete the reward.
    await ctx.db.delete(rewardId);
  },
});

// =================================================================
// Redemption Fulfillment
// =================================================================

/**
 * Find users by name or email for the admin's client.
 */
export const findUsersByNameOrEmail = query({
  args: { searchQuery: v.optional(v.string()) },
  handler: async (ctx, { searchQuery }) => {
    const { user: adminUser } = await requireAdminOrStaffRole(ctx);

    if (!searchQuery) {
      return [];
    }

    const users = await ctx.db
      .query('users')
      .withSearchIndex('by_searchText', (q) =>
        q.search('searchText', searchQuery).eq('clientId', adminUser.clientId!)
      )
      .take(10);

    return users;
  },
});

/**
 * Get all redemptions for the client, with optional filters.
 */
export const getRedemptionsForClient = query({
  args: {
    status: v.optional(v.union(v.literal('Pending'), v.literal('Fulfilled'))),
    userId: v.optional(v.id('users')),
  },
  handler: async (ctx, { status, userId }) => {
    const { user: adminUser } = await requireAdminOrStaffRole(ctx);

    let redemptionsQuery = ctx.db
      .query('userRedemptions')
      .filter((q) => q.eq(q.field('clientId'), adminUser.clientId));

    if (status) {
      redemptionsQuery = redemptionsQuery.filter((q) =>
        q.eq(q.field('status'), status)
      );
    }
    if (userId) {
      redemptionsQuery = redemptionsQuery.filter((q) =>
        q.eq(q.field('userId'), userId)
      );
    }

    const redemptions = await redemptionsQuery.order('desc').collect();

    // Join with user and reward data
    return Promise.all(
      redemptions.map(async (r) => {
        const user = await ctx.db.get(r.userId);
        const reward = await ctx.db.get(r.rewardId);
        return {
          ...r,
          userName: user
            ? `${user.firstName} ${user.lastName}`
            : 'Unknown User',
          rewardName: reward ? reward.name : (r.rewardName ?? 'Deleted Reward'),
        };
      })
    );
  },
});

/**
 * Fulfill a pending redemption.
 */
export const fulfillRedemption = mutation({
  args: { redemptionId: v.id('userRedemptions') },
  handler: async (ctx, { redemptionId }) => {
    const { user: staffUser } = await requireAdminOrStaffRole(ctx);

    const redemption = await ctx.db.get(redemptionId);
    if (!redemption) {
      throw new Error('Redemption not found.');
    }

    if (redemption.clientId !== staffUser.clientId) {
      throw new Error('This redemption does not belong to your client.');
    }

    if (redemption.status === 'Fulfilled') {
      throw new Error('This reward has already been fulfilled.');
    }

    await ctx.db.patch(redemptionId, {
      status: 'Fulfilled',
      fulfilledAt: Date.now(),
      fulfilledBy: staffUser._id,
    });
  },
});
