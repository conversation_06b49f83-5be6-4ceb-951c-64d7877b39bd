/**
 * Mariana-Tek synchronization service
 * Implements polling-based integration for check-in events
 */

import { internalAction, internalMutation } from '../_generated/server';
import { v } from 'convex/values';
import { internal } from '../_generated/api';
import { Id } from '../_generated/dataModel';

/**
 * Main sync action for Mariana-Tek integration
 * Called by cron job every 5 minutes during business hours
 */
export const syncMarianaTekCheckIns = internalAction({
  args: {
    clientId: v.optional(v.id('clients')), // If not provided, sync all clients
  },
  async handler(ctx, args) {
    console.log('Starting Mariana-Tek sync process...');

    try {
      // Get all clients with active Mariana-Tek integrations
      const clientsToSync = args.clientId 
        ? [args.clientId]
        : await ctx.runQuery(internal.functions.marianaTekSync.getClientsWithMarianaTekIntegration);

      let totalProcessed = 0;
      let totalMatched = 0;
      let totalUnmatched = 0;

      for (const clientId of clientsToSync) {
        try {
          const result = await ctx.runAction(internal.functions.marianaTekSync.syncClientCheckIns, {
            clientId,
          });

          totalProcessed += result.eventsProcessed;
          totalMatched += result.eventsMatched;
          totalUnmatched += result.eventsUnmatched;

          console.log(`Synced client ${clientId}: ${result.eventsProcessed} events processed`);
        } catch (error) {
          console.error(`Failed to sync client ${clientId}:`, error);
          
          // Update sync status with error
          await ctx.runMutation(internal.functions.marianaTekSync.updateSyncStatus, {
            clientId,
            provider: 'marianaTek',
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            eventsProcessed: 0,
            eventsMatched: 0,
            eventsUnmatched: 0,
          });
        }
      }

      console.log(`Mariana-Tek sync completed. Total: ${totalProcessed} processed, ${totalMatched} matched, ${totalUnmatched} unmatched`);

      return {
        success: true,
        clientsProcessed: clientsToSync.length,
        totalEventsProcessed: totalProcessed,
        totalEventsMatched: totalMatched,
        totalEventsUnmatched: totalUnmatched,
      };
    } catch (error) {
      console.error('Mariana-Tek sync failed:', error);
      throw error;
    }
  },
});

/**
 * Sync check-ins for a specific client
 */
export const syncClientCheckIns = internalAction({
  args: {
    clientId: v.id('clients'),
  },
  async handler(ctx, args) {
    // Get API credentials
    const credentials = await ctx.runQuery(internal.functions.integrations.getApiCredentials, {
      clientId: args.clientId,
      provider: 'marianaTek',
    });

    if (!credentials || !credentials.isActive) {
      console.log(`No active Mariana-Tek credentials for client ${args.clientId}`);
      return { eventsProcessed: 0, eventsMatched: 0, eventsUnmatched: 0 };
    }

    // Get last sync time
    const lastSyncTime = await ctx.runQuery(internal.functions.marianaTekSync.getLastSyncTime, {
      clientId: args.clientId,
      provider: 'marianaTek',
    });

    // Default to 1 hour ago if no previous sync
    const since = lastSyncTime ? new Date(lastSyncTime) : new Date(Date.now() - 60 * 60 * 1000);

    try {
      // Fetch check-in events from Mariana-Tek
      const { MarianaTekService } = await import('../lib/MarianaTekService');
      const service = new MarianaTekService(credentials.encryptedApiKey);
      const checkInEvents = await service.getRecentCheckIns(since);

      console.log(`Found ${checkInEvents.length} check-in events for client ${args.clientId}`);

      let eventsMatched = 0;
      let eventsUnmatched = 0;

      // Process each check-in event
      for (const event of checkInEvents) {
        try {
          const result = await ctx.runAction(internal.functions.marianaTekSync.processCheckInEvent, {
            clientId: args.clientId,
            event,
          });

          if (result.matched) {
            eventsMatched++;
          } else {
            eventsUnmatched++;
          }
        } catch (error) {
          console.error('Failed to process check-in event:', event, error);
          eventsUnmatched++;
        }
      }

      // Update sync status
      await ctx.runMutation(internal.functions.marianaTekSync.updateSyncStatus, {
        clientId: args.clientId,
        provider: 'marianaTek',
        status: 'success',
        eventsProcessed: checkInEvents.length,
        eventsMatched,
        eventsUnmatched,
      });

      return {
        eventsProcessed: checkInEvents.length,
        eventsMatched,
        eventsUnmatched,
      };
    } catch (error) {
      console.error(`Failed to sync check-ins for client ${args.clientId}:`, error);
      
      // Update sync status with error
      await ctx.runMutation(internal.functions.marianaTekSync.updateSyncStatus, {
        clientId: args.clientId,
        provider: 'marianaTek',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        eventsProcessed: 0,
        eventsMatched: 0,
        eventsUnmatched: 0,
      });

      throw error;
    }
  },
});

/**
 * Process a single check-in event
 */
export const processCheckInEvent = internalAction({
  args: {
    clientId: v.id('clients'),
    event: v.object({
      reservationId: v.string(),
      userId: v.string(),
      userEmail: v.string(),
      userName: v.optional(v.string()),
      classType: v.string(),
      checkedInAt: v.string(), // ISO date string
      externalTimestamp: v.number(),
    }),
  },
  async handler(ctx, args) {
    const { event } = args;

    // Find user in our system
    const userMatch = await ctx.runQuery(internal.functions.integrations.findUserByExternalId, {
      provider: 'marianaTek',
      externalUserId: event.userId,
      email: event.userEmail,
      clientId: args.clientId,
    });

    if (userMatch) {
      // Link external ID if matched by email
      if (userMatch.matchType === 'email') {
        await ctx.runMutation(internal.functions.integrations.linkExternalIdToUser, {
          userId: userMatch.user._id,
          provider: 'marianaTek',
          externalUserId: event.userId,
        });
      }

      // Log the activity
      const activityResult = await ctx.runMutation(internal.functions.activities.internalLogActivity, {
        userId: userMatch.user._id,
        activityTypeKey: 'class_attendance', // Default activity type
        source: 'api_poll',
        externalReference: {
          provider: 'marianaTek',
          externalId: event.reservationId,
          externalTimestamp: event.externalTimestamp,
        },
      });

      console.log(`Logged activity for user ${userMatch.user.email}: ${activityResult.pointsAwarded} points awarded`);

      return { matched: true, duplicate: activityResult.duplicate };
    } else {
      // Store as unmatched event for admin review
      await ctx.runMutation(internal.functions.integrations.storeUnmatchedEvent, {
        clientId: args.clientId,
        provider: 'marianaTek',
        externalUserId: event.userId,
        externalUserEmail: event.userEmail,
        externalUserName: event.userName,
        eventType: 'check_in',
        eventData: event,
      });

      console.log(`Stored unmatched check-in event for external user ${event.userEmail}`);

      return { matched: false, duplicate: false };
    }
  },
});

/**
 * Get clients with active Mariana-Tek integrations
 */
export const getClientsWithMarianaTekIntegration = internalQuery({
  async handler(ctx) {
    const credentials = await ctx.db
      .query('clientApiCredentials')
      .filter((q) => 
        q.and(
          q.eq(q.field('provider'), 'marianaTek'),
          q.eq(q.field('isActive'), true)
        )
      )
      .collect();

    return credentials.map(cred => cred.clientId);
  },
});

/**
 * Get last sync time for a client and provider
 */
export const getLastSyncTime = internalQuery({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
  },
  async handler(ctx, args) {
    const syncStatus = await ctx.db
      .query('integrationSyncStatus')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', args.clientId).eq('provider', args.provider)
      )
      .first();

    return syncStatus?.lastSyncAt;
  },
});

/**
 * Update sync status for a client and provider
 */
export const updateSyncStatus = internalMutation({
  args: {
    clientId: v.id('clients'),
    provider: v.string(),
    status: v.union(v.literal('success'), v.literal('failed'), v.literal('partial')),
    error: v.optional(v.string()),
    eventsProcessed: v.number(),
    eventsMatched: v.number(),
    eventsUnmatched: v.number(),
  },
  async handler(ctx, args) {
    const existing = await ctx.db
      .query('integrationSyncStatus')
      .withIndex('by_client_and_provider', (q) => 
        q.eq('clientId', args.clientId).eq('provider', args.provider)
      )
      .first();

    const now = Date.now();
    const nextSyncAt = now + (5 * 60 * 1000); // Next sync in 5 minutes

    if (existing) {
      await ctx.db.patch(existing._id, {
        lastSyncAt: now,
        lastSyncStatus: args.status,
        lastSyncError: args.error,
        eventsProcessed: args.eventsProcessed,
        eventsMatched: args.eventsMatched,
        eventsUnmatched: args.eventsUnmatched,
        nextSyncAt,
      });
    } else {
      await ctx.db.insert('integrationSyncStatus', {
        clientId: args.clientId,
        provider: args.provider,
        lastSyncAt: now,
        lastSyncStatus: args.status,
        lastSyncError: args.error,
        eventsProcessed: args.eventsProcessed,
        eventsMatched: args.eventsMatched,
        eventsUnmatched: args.eventsUnmatched,
        nextSyncAt,
      });
    }
  },
});
