import {
  query,
  QueryCtx,
  internalMutation,
  mutation,
} from '../_generated/server';
import { v } from 'convex/values';
import {
  getUserTierHistory,
  getTierDistribution,
  getUserTierPercentile,
  calculateUserTier,
  TIER_THRESHOLDS,
} from '../lib/TierService';
import { createLogger } from '../lib/logger';
import {
  requireAuthenticatedUser,
  getCurrentAuthenticatedUser,
} from '../lib/authHelpers';
import { generateAnonymousName } from '../lib/anonymousNames';
import { v4 as uuidv4 } from 'uuid';
import { internal } from '../_generated/api';

/**
 * Helper function to get a user by their Clerk ID.
 * @param {QueryCtx} ctx - The query context
 * @param {string} clerkUserId - The Clerk user ID to search for
 * @returns {Promise<Doc<'users'> | null>} The user document or null if not found
 */
export async function getUserByClerkId(ctx: QueryCtx, clerkUserId: string) {
  return await ctx.db
    .query('users')
    .withIndex('by_clerk_user_id', (q) => q.eq('clerkUserId', clerkUserId))
    .unique();
}

/**
 * READ-ONLY query to get the current authenticated user.
 * @returns {Promise<Doc<'users'> | null>} The current user or null if not authenticated/found
 */
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const logger = createLogger('getCurrentUser');

    try {
      const user = await getCurrentAuthenticatedUser(ctx);

      if (user) {
        logger.debug('User found', { userId: user._id });
      } else {
        logger.debug('No authenticated user found');
      }

      return user;
    } catch (error) {
      logger.error('Failed to get current user', error as Error);
      return null;
    }
  },
});

/**
 * Gets or creates a user in a single, atomic operation.
 * This is the primary entry point for the client to get user data.
 * @returns {Promise<Doc<'users'>>} The user document
 */
export const getOrCreateUser = mutation({
  args: {},
  handler: async (ctx) => {
    const logger = createLogger('getOrCreateUser');
    try {
      const identity = await ctx.auth.getUserIdentity();

      if (!identity) {
        logger.warn('Unauthenticated call to getOrCreateUser.');
        throw new Error('Unauthenticated. Please sign in.');
      }

      const user = await getUserByClerkId(ctx, identity.subject);

      if (user !== null) {
        logger.debug('User found.', { userId: user._id });

        // Backfill leaderboard settings for existing users if they are missing.
        if (user.leaderboardSettings === undefined) {
          logger.info('User found without leaderboardSettings. Backfilling.', {
            userId: user._id,
          });
          const anonymousName = await ctx.runQuery(
            internal.functions.anonymousNameThemes
              .internalGetAnonymousNameForClient,
            { clientId: user.clientId! }
          );
          await ctx.db.patch(user._id, {
            leaderboardSettings: {
              showRealName: false,
              anonymousName: anonymousName,
              avatarSeed: crypto.randomUUID(),
            },
          });
          // Refetch user to return the updated document
          const updatedUser = await ctx.db.get(user._id);
          return updatedUser!;
        }

        return user;
      }

      logger.info(
        'User not found via getUserByClerkId, proceeding with creation.'
      );

      // To prevent race conditions, check one more time inside the mutation
      const potentialUser = await getUserByClerkId(ctx, identity.subject);
      if (potentialUser) {
        logger.info('User found on second check, returning existing user.');
        return potentialUser;
      }

      const handleBarClient = await ctx.db
        .query('clients')
        .withIndex('by_slug', (q) => q.eq('slug', 'the-handle-bar'))
        .unique();

      if (!handleBarClient) {
        logger.error(
          'Default client "the-handle-bar" not found. Cannot create user.'
        );
        throw new Error('Default client not found. Please seed the database.');
      }

      const anonymousName = await ctx.runQuery(
        internal.functions.anonymousNameThemes
          .internalGetAnonymousNameForClient,
        { clientId: handleBarClient._id }
      );
      const avatarSeed = crypto.randomUUID();

      logger.info(
        `Generated anonymous identity for new user: ${anonymousName}`
      );

      const newUser = {
        clerkUserId: identity.subject,
        email: identity.email!,
        firstName: identity.givenName,
        lastName: identity.familyName,
        points: 0,
        tier: 'Bronze',
        clientId: handleBarClient._id,
        role: 'user',
        searchText:
          `${identity.givenName ?? ''} ${identity.familyName ?? ''} ${identity.email!}`.toLowerCase(),
        leaderboardSettings: {
          showRealName: false,
          anonymousName,
          avatarSeed,
        },
      };

      const newUserId = await ctx.db.insert('users', newUser);
      const createdUser = await ctx.db.get(newUserId);

      if (!createdUser) {
        // This should theoretically never happen
        logger.error('Failed to retrieve user immediately after creation.');
        throw new Error('User creation failed.');
      }

      logger.info('New user created successfully via getOrCreateUser', {
        userId: createdUser._id,
      });

      return createdUser;
    } catch (error) {
      logger.error('Error in getOrCreateUser', error as Error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get a user's milestone progress with detailed milestone information.
 * This can only be called when we know the user exists.
 * @returns {Promise<Array>} Array of user milestone progress records with milestone details
 */
export const getUserMilestones = query({
  args: {},
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();

      if (!identity) {
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);

      if (!user) {
        return [];
      }

      // Get the user's milestone progress
      const milestoneProgress = await ctx.db
        .query('userMilestoneProgress')
        .withIndex('by_user_id', (q) => q.eq('userId', user._id))
        .order('desc') // Show most recent achievements first
        .collect();

      // Enhance each milestone progress with milestone details
      const enhancedMilestones = await Promise.all(
        milestoneProgress.map(async (progress) => {
          try {
            // The milestoneId is stored as the milestone's _id
            const milestone = await ctx.db.get(progress.milestoneId as any);

            return {
              ...progress,
              milestoneName: milestone?.name || 'Unknown Milestone',
              milestoneDescription:
                milestone?.description || 'No description available',
              milestoneConditions: milestone?.conditions || null,
              isRepeatable: milestone?.isRepeatable || false,
            };
          } catch (error) {
            console.error(
              'Error fetching milestone details for ID:',
              progress.milestoneId,
              error
            );
            // Return progress with fallback values if milestone lookup fails
            return {
              ...progress,
              milestoneName: 'Unknown Milestone',
              milestoneDescription: 'Milestone details unavailable',
              milestoneConditions: null,
              isRepeatable: false,
            };
          }
        })
      );

      return enhancedMilestones;
    } catch (error) {
      console.error('Error in getUserMilestones:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get user's tier progress and information.
 * @returns {Promise<Object | null>} Tier progress data or null if user not found
 */
export const getUserTierProgress = query({
  args: {},
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        return null;
      }

      const tierProgress = calculateUserTier({
        currentPoints: user.points,
        currentTier: user.tier,
      });

      const tierHistory = await getUserTierHistory(ctx, user._id);

      const userPercentile = await getUserTierPercentile(
        ctx,
        user.tier,
        user.clientId || undefined
      );

      return {
        currentTier: user.tier,
        points: user.points,
        tierProgress,
        tierHistory,
        userPercentile,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('Error in getUserTierProgress:', error);
      throw error;
    }
  },
});

/**
 * READ-ONLY query to get tier distribution statistics
 */
export const getTierStatistics = query({
  args: {},
  handler: async (ctx) => {
    console.log('📊 getTierStatistics called');

    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.error('❌ No identity found in getTierStatistics');
        throw new Error('Authentication required');
      }

      const user = await getUserByClerkId(ctx, identity.subject);
      if (!user) {
        console.warn('⚠️ User not found in getTierStatistics');
        return null;
      }

      // Get tier distribution for user's client
      const distribution = await getTierDistribution(
        ctx,
        user.clientId || undefined
      );

      console.log('📈 Tier distribution calculated:', {
        totalUsers: distribution.totalUsers,
        tierCounts: distribution.tierDistribution,
      });

      return {
        totalUsers: distribution.totalUsers,
        tierDistribution: distribution.tierDistribution,
        tierThresholds: TIER_THRESHOLDS,
      };
    } catch (error) {
      console.error('💥 Error in getTierStatistics:', error);
      throw error;
    }
  },
});

/**
 * Internal mutation to create or update a user from webhook
 */
export const internalStoreOrUpdateUser = internalMutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getUserByClerkId(ctx, args.clerkUserId);
    const logger = createLogger('internalStoreOrUpdateUser', {
      clerkUserId: args.clerkUserId,
    });

    if (user) {
      logger.info('User found, updating document.');
      await ctx.db.patch(user._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        searchText:
          `${args.firstName ?? ''} ${args.lastName ?? ''} ${args.email}`.toLowerCase(),
      });
      logger.info('User document updated successfully.');
      return;
    }

    logger.info('User not found, creating new document.');

    const handleBarClient = await ctx.db
      .query('clients')
      .withIndex('by_slug', (q) => q.eq('slug', 'the-handle-bar'))
      .unique();

    if (!handleBarClient) {
      logger.error(
        'Default client "the-handle-bar" not found. Cannot create user.'
      );
      throw new Error('Default client not found. Please seed the database.');
    }

    const anonymousName = await ctx.runQuery(
      internal.functions.anonymousNameThemes.internalGetAnonymousNameForClient,
      { clientId: handleBarClient._id }
    );
    const avatarSeed = crypto.randomUUID();

    logger.info(
      `Generated anonymous identity: ${anonymousName}, seed: ${avatarSeed}`
    );

    const newUser = {
      clerkUserId: args.clerkUserId,
      email: args.email,
      firstName: args.firstName,
      lastName: args.lastName,
      points: 0,
      tier: 'Bronze',
      clientId: handleBarClient._id,
      role: 'user', // Default role for new users
      searchText:
        `${args.firstName ?? ''} ${args.lastName ?? ''} ${args.email}`.toLowerCase(),
      leaderboardSettings: {
        showRealName: false,
        anonymousName,
        avatarSeed,
      },
    };

    await ctx.db.insert('users', newUser);
    logger.info('New user created successfully.');
  },
});

/**
 * Internal mutation to set a user's role from a webhook.
 */
export const internalSetUserRole = internalMutation({
  args: { clerkUserId: v.string(), role: v.string() },
  handler: async (ctx, { clerkUserId, role }) => {
    const user = await getUserByClerkId(ctx, clerkUserId);

    if (user) {
      await ctx.db.patch(user._id, { role });
      console.log(`✅ Updated role for user ${clerkUserId} to "${role}"`);
    } else {
      console.warn(
        `⚠️ Webhook tried to set role for non-existent user: ${clerkUserId}`
      );
    }
  },
});

/**
 * INTERNAL-ONLY query to get a user by their internal Convex ID.
 * This is for use in other backend functions, not from the client.
 * @param {string} id - The user's internal _id.
 * @returns {Promise<Doc<'users'> | null>} The user document or null if not found.
 */
export const get = query({
  args: { id: v.id('users') },
  handler: async (ctx, { id }) => {
    return await ctx.db.get(id);
  },
});

/**
 * INTERNAL-ONLY query to get a client by its internal Convex ID.
 * @param {string} clientId - The client's internal _id.
 * @returns {Promise<Doc<'clients'> | null>} The client document or null if not found.
 */
export const getClient = query({
  args: { clientId: v.id('clients') },
  handler: async (ctx, { clientId }) => {
    return await ctx.db.get(clientId);
  },
});

/**
 * READ-ONLY query to fetch users for the leaderboard.
 * Fetches top 25 users for the current user's client, ordered by points earned this month.
 * @returns {Promise<Array<Doc<'users'>>>} A list of users for the leaderboard.
 */
export const getLeaderboard = query({
  args: {},
  handler: async (ctx) => {
    const logger = createLogger('getLeaderboard');
    try {
      const user = await requireAuthenticatedUser(ctx);
      if (!user.clientId) {
        logger.warn('User has no clientId, cannot fetch leaderboard.', {
          userId: user._id,
        });
        return [];
      }

      // 1. Get all users for the client
      const usersInClient = await ctx.db
        .query('users')
        .filter((q) => q.eq(q.field('clientId'), user.clientId))
        .collect();

      const userIdsInClient = new Set(usersInClient.map((u) => u._id));
      const usersById = new Map(usersInClient.map((u) => [u._id, u]));

      // 2. Calculate start of current month
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfMonthTimestamp = startOfMonth.getTime();

      // 3. Fetch all milestone progress for all users this month
      const allProgressThisMonth = await ctx.db
        .query('userMilestoneProgress')
        .withIndex('by_achieved_at', (q) =>
          q.gte('achievedAt', startOfMonthTimestamp)
        )
        .collect();

      // 4. Aggregate points for users in the current client
      const userPointsThisMonth = new Map<Id<'users'>, number>();
      for (const progress of allProgressThisMonth) {
        if (userIdsInClient.has(progress.userId)) {
          const currentPoints = userPointsThisMonth.get(progress.userId) ?? 0;
          userPointsThisMonth.set(
            progress.userId,
            currentPoints + progress.pointsEarned
          );
        }
      }

      // 5. Sort users by monthly points
      const sortedUserIds = Array.from(userPointsThisMonth.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 25)
        .map((entry) => entry[0]);

      // 6. Format the final leaderboard data
      const leaderboard = sortedUserIds.map((id) => {
        const userDoc = usersById.get(id)!;
        return {
          ...userDoc,
          monthlyPoints: userPointsThisMonth.get(id) ?? 0,
        };
      });

      logger.debug('Successfully fetched leaderboard data.', {
        clientId: user.clientId,
        userCount: leaderboard.length,
      });

      return leaderboard;
    } catch (error) {
      logger.error('Failed to get leaderboard', error as Error);
      return []; // Return empty array on error to prevent client crashes
    }
  },
});

/**
 * Mutation for a user to update their own leaderboard settings.
 * @param {boolean} showRealName - Whether to show the user's real name.
 */
export const updateLeaderboardSettings = mutation({
  args: {
    showRealName: v.boolean(),
  },
  handler: async (ctx, { showRealName }) => {
    const user = await requireAuthenticatedUser(ctx);
    const logger = createLogger('updateLeaderboardSettings');

    try {
      const currentSettings = user.leaderboardSettings;

      if (!currentSettings) {
        const anonymousName = await ctx.runQuery(
          internal.functions.anonymousNameThemes
            .internalGetAnonymousNameForClient,
          { clientId: user.clientId! }
        );
        // This is a backfill for users who don't have the setting object yet.
        await ctx.db.patch(user._id, {
          leaderboardSettings: {
            showRealName,
            anonymousName: anonymousName,
            avatarSeed: crypto.randomUUID(),
          },
        });
        logger.info('Successfully backfilled leaderboard settings for user.', {
          userId: user._id,
        });
      } else {
        await ctx.db.patch(user._id, {
          leaderboardSettings: {
            ...currentSettings,
            showRealName,
          },
        });
      }
    } catch (error) {
      logger.error('Failed to update leaderboard settings.', {
        userId: user._id,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new Error('Failed to update settings. Please try again.');
    }
  },
});
