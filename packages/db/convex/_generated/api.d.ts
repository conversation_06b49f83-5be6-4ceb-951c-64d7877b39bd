/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as dev from "../dev.js";
import type * as functions_activities from "../functions/activities.js";
import type * as functions_activityTypes from "../functions/activityTypes.js";
import type * as functions_admin from "../functions/admin.js";
import type * as functions_anonymousNameThemes from "../functions/anonymousNameThemes.js";
import type * as functions_cheers from "../functions/cheers.js";
import type * as functions_clientConfigs from "../functions/clientConfigs.js";
import type * as functions_emails from "../functions/emails.js";
import type * as functions_history from "../functions/history.js";
import type * as functions_milestones from "../functions/milestones.js";
import type * as functions_rewards from "../functions/rewards.js";
import type * as functions_userRedemptions from "../functions/userRedemptions.js";
import type * as functions_users from "../functions/users.js";
import type * as http from "../http.js";
import type * as lib_MilestoneService from "../lib/MilestoneService.js";
import type * as lib_TierService from "../lib/TierService.js";
import type * as lib_anonymousNames from "../lib/anonymousNames.js";
import type * as lib_authHelpers from "../lib/authHelpers.js";
import type * as lib_logger from "../lib/logger.js";
import type * as seed from "../seed.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  dev: typeof dev;
  "functions/activities": typeof functions_activities;
  "functions/activityTypes": typeof functions_activityTypes;
  "functions/admin": typeof functions_admin;
  "functions/anonymousNameThemes": typeof functions_anonymousNameThemes;
  "functions/cheers": typeof functions_cheers;
  "functions/clientConfigs": typeof functions_clientConfigs;
  "functions/emails": typeof functions_emails;
  "functions/history": typeof functions_history;
  "functions/milestones": typeof functions_milestones;
  "functions/rewards": typeof functions_rewards;
  "functions/userRedemptions": typeof functions_userRedemptions;
  "functions/users": typeof functions_users;
  http: typeof http;
  "lib/MilestoneService": typeof lib_MilestoneService;
  "lib/TierService": typeof lib_TierService;
  "lib/anonymousNames": typeof lib_anonymousNames;
  "lib/authHelpers": typeof lib_authHelpers;
  "lib/logger": typeof lib_logger;
  seed: typeof seed;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
