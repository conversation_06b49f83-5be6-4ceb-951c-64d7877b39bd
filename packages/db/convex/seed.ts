import { internalMutation } from './_generated/server';

// This configuration is used to seed your database with an initial client.
const theHandleBarSeedConfig = {
  clientName: 'The Handle Bar Fitness Studio',
  slug: 'the-handle-bar',
  branding: {
    primaryColor: '#4F46E5',
    logoUrl: '/assets/clients/the-handle-bar/logo.png',
  },
  features: {
    tiersEnabled: true,
    leaderboardsEnabled: false,
    socialSharingActive: true,
  },
  rewards: [
    {
      id: 'smoothie-theHandleBar',
      name: 'Free Smoothie',
      description: 'Enjoy a refreshing post-workout smoothie on us.',
      cost: 500,
    },
    {
      id: 'waterbottle-theHandleBar',
      name: 'Branded Water Bottle',
      description: 'Stay hydrated with our stylish water bottle.',
      cost: 1500,
    },
    {
      id: 'tshirt-theHandleBar',
      name: 'Studio T-Shirt',
      description: 'Represent The Handle Bar with our official tee.',
      cost: 2500,
    },
    {
      id: 'guestpass-theHandleBar',
      name: 'Free Guest Pass',
      description: 'Bring a friend to a class for free.',
      cost: 3000,
    },
    {
      id: 'merch_10_off-theHandleBar',
      name: '10% Off Merchandise',
      description: 'Get a discount on your next apparel purchase.',
      cost: 1000,
    },
    {
      id: 'pt_20_off-theHandleBar',
      name: '20% Off Personal Training',
      description: 'Level up your fitness with a discounted PT session.',
      cost: 5000,
    },
    {
      id: 'yogamat-theHandleBar',
      name: 'Premium Yoga Mat',
      description: 'A high-quality mat for your practice.',
      cost: 6000,
    },
    {
      id: 'classpack_5-theHandleBar',
      name: '5-Class Pack Discount',
      description: 'Get 15% off your next 5-class pack purchase.',
      cost: 7500,
    },
    {
      id: 'nutrition-consult-theHandleBar',
      name: 'Nutrition Consultation',
      description: 'A one-on-one session with our nutrition expert.',
      cost: 10000,
    },
    {
      id: 'month_free-theHandleBar',
      name: 'One Month Free Membership',
      description:
        'The ultimate prize! Enjoy a full month of unlimited classes.',
      cost: 25000,
    },
  ],
  dashboardLayout: {
    layout: 'grid',
    widgets: [
      { type: 'points_summary', position: 1, title: 'My Studio A Points' },
      { type: 'recent_activities', position: 2, config: { limit: 3 } },
      {
        type: 'milestone_progress_overview',
        position: 3,
        title: 'My Badge Collection',
      },
    ],
  },
  activityTypes: [
    {
      name: 'Class Attendance',
      key: 'class_attendance',
      iconUrl: 'calendar-check-in-01',
      points: 10,
    },
    {
      name: 'Personal Training',
      key: 'personal_training',
      iconUrl: 'dumbbell-01',
      points: 50,
    },
    {
      name: 'Workshop',
      key: 'workshop',
      iconUrl: 'equipment-gym-01',
      points: 25,
    },
  ],
};

export default internalMutation({
  handler: async (ctx) => {
    console.log('🌱 Seeding database...');

    const clientSlug = theHandleBarSeedConfig.slug;

    const existingClient = await ctx.db
      .query('clients')
      .withIndex('by_slug', (q) => q.eq('slug', clientSlug))
      .unique();

    let clientIdToUse;

    if (existingClient) {
      console.log(
        `Client "${clientSlug}" (ID: ${existingClient._id}) already exists. Updating config.`
      );
      clientIdToUse = existingClient._id;
      await ctx.db.patch(existingClient._id, {
        name: theHandleBarSeedConfig.clientName,
      });
    } else {
      clientIdToUse = await ctx.db.insert('clients', {
        name: theHandleBarSeedConfig.clientName,
        slug: clientSlug,
        createdAt: Date.now(),
      });
      console.log(
        `🌿 Seeded new client "${clientSlug}" with ID: ${clientIdToUse}`
      );
    }

    const existingClientConfig = await ctx.db
      .query('clientConfiguration')
      .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
      .unique();

    const configPayload: any = {
      clientId: clientIdToUse,
      branding: theHandleBarSeedConfig.branding,
      features: theHandleBarSeedConfig.features,
      rewards: theHandleBarSeedConfig.rewards,
      dashboardLayout: theHandleBarSeedConfig.dashboardLayout,
    };

    // Rewards are now managed in their own table, so we remove them from the config payload.
    delete configPayload.rewards;

    if (existingClientConfig) {
      await ctx.db.patch(existingClientConfig._id, configPayload);
      console.log(`⚙️  Updated configuration for client ID: ${clientIdToUse}`);
    } else {
      await ctx.db.insert('clientConfiguration', configPayload);
      console.log(
        `⚙️  Seeded new configuration for client ID: ${clientIdToUse}`
      );
    }

    // Seed Activity Types
    for (const activityType of theHandleBarSeedConfig.activityTypes) {
      const existing = await ctx.db
        .query('activityTypes')
        .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
        .filter((q) => q.eq(q.field('key'), activityType.key))
        .first();

      if (existing) {
        // Always patch to ensure seed data is up-to-date
        await ctx.db.patch(existing._id, {
          iconUrl: activityType.iconUrl,
          points: activityType.points,
        });
        console.log(`🔧 Patched activity type: ${activityType.name}`);
      } else {
        await ctx.db.insert('activityTypes', {
          clientId: clientIdToUse,
          ...activityType,
        });
        console.log(`🏃‍♀️ Seeded activity type: ${activityType.name}`);
      }
    }

    // --- New Milestones Seeding ---
    console.log('🌱 Seeding new milestones...');

    let adminUser = await ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('role'), 'admin'))
      .first();

    if (!adminUser) {
      adminUser = await ctx.db
        .query('users')
        .filter((q) => q.eq(q.field('email'), '<EMAIL>'))
        .first();
    }

    if (!adminUser) {
      adminUser = await ctx.db.query('users').first();
    }

    const milestonesToSeed = [
      {
        name: 'First Class',
        description: 'Complete your first class.',
        iconUrl: 'award-01',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 1,
        },
        rewards: [{ type: 'points', value: 50 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Triple Threat',
        description: 'Attend 3 classes.',
        iconUrl: 'award-02',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 3,
        },
        rewards: [{ type: 'points', value: 100 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Five Star',
        description: 'Attend 5 classes.',
        iconUrl: 'star-award-01',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 5,
        },
        rewards: [{ type: 'points', value: 150 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Tenacious Ten',
        description: 'Attend 10 classes.',
        iconUrl: 'medal-01',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 10,
        },
        rewards: [{ type: 'points', value: 200 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Perfect Twenty',
        description: 'Attend 20 classes.',
        iconUrl: 'medal-02',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 20,
        },
        rewards: [{ type: 'points', value: 300 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Silver Fifty',
        description: 'Attend 50 classes.',
        iconUrl: 'medal-second-place',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 50,
        },
        rewards: [{ type: 'points', value: 500 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Golden Century',
        description: 'Attend 100 classes.',
        iconUrl: 'champion',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'class_attendance',
          countThreshold: 100,
        },
        rewards: [{ type: 'points', value: 1000 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'First Personal Training',
        description: 'Complete your first Personal Training session.',
        iconUrl: 'dumbbell-01',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'personal_training',
          countThreshold: 1,
        },
        rewards: [{ type: 'points', value: 150 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Workshop Wonder',
        description: 'Attend your first workshop.',
        iconUrl: 'presentation-01',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'workshop',
          countThreshold: 1,
        },
        rewards: [{ type: 'points', value: 100 }],
        isRepeatable: false,
        isEnabled: true,
      },
      {
        name: 'Dedicated Athlete',
        description: 'Attend 5 Personal Training sessions.',
        iconUrl: 'dumbbell-02',
        triggerType: 'activity_count',
        conditions: {
          activityTypeMatcher: 'personal_training',
          countThreshold: 5,
        },
        rewards: [{ type: 'points', value: 500 }],
        isRepeatable: false,
        isEnabled: true,
      },
    ];

    if (adminUser) {
      for (const milestone of milestonesToSeed) {
        const existing = await ctx.db
          .query('milestones')
          .withIndex('by_client_id_and_name', (q) =>
            q.eq('clientId', clientIdToUse).eq('name', milestone.name)
          )
          .first();

        if (!existing) {
          await ctx.db.insert('milestones', {
            clientId: clientIdToUse,
            ...milestone,
            createdBy: adminUser._id,
            lastModifiedBy: adminUser._id,
          });
          console.log(`🌱 Seeded milestone: ${milestone.name}`);
        }
      }
    } else {
      console.warn(
        '⚠️ Could not run milestone seeding. No users found in the database.'
      );
    }

    // --- New Rewards Seeding ---
    console.log('🎁 Seeding rewards...');
    const rewardsFromPRD = [
      {
        name: 'Free Towel Service for a Day',
        cost: 100,
        description:
          "Forget your towel? We've got you covered. Enjoy complimentary towel service.",
        imageUrl: 'towel',
        isActive: true,
      },
      {
        name: 'Choice of Post-Workout Drink',
        cost: 160,
        description:
          'Refuel after your ride! Your choice of a free smoothie or kombucha on tap.',
        imageUrl: 'beverage-02',
        isActive: true,
      },
      {
        name: 'Branded Water Bottle',
        cost: 350,
        description: 'Stay hydrated in style with our signature water bottle.',
        imageUrl: 'bottle',
        isActive: true,
      },
      {
        name: 'Bring a Friend for Free',
        cost: 750,
        description: 'Share the sweat! Bring a friend to any class on us.',
        imageUrl: 'user-add-01',
        isActive: true,
      },
      {
        name: '$10 Off Merchandise',
        cost: 1000,
        description:
          'Treat yourself. Get $10 off any item from our merchandise collection.',
        imageUrl: 'ticket-01',
        isActive: true,
      },
      {
        name: 'Early Access to Class Booking',
        cost: 1500,
        description:
          'Book your favorite bike, worry-free. Get 24-hour early access to booking.',
        imageUrl: 'calendar-check-02',
        isActive: true,
      },
      {
        name: '30-Min Personal Form Assessment',
        cost: 2500,
        description:
          'Perfect your form in a 1-on-1 session with one of our expert instructors.',
        imageUrl: 'activity',
        isActive: true,
      },
      {
        name: 'The Handle Bar Signature Sweatshirt',
        cost: 4000,
        description:
          'The ultimate cozy reward. Our super-soft, signature branded sweatshirt.',
        imageUrl: 't-shirt-01',
        isActive: true,
      },
      {
        name: 'Free Entry to a 2-Hour Workshop',
        cost: 5000,
        description:
          'Deepen your practice. Gain free entry to any upcoming special workshop.',
        imageUrl: 'award-04',
        isActive: true,
      },
      {
        name: 'One Month of Unlimited Classes',
        cost: 10000,
        description:
          'The ultimate prize! A full month of unlimited classes on us.',
        imageUrl: 'trophy-01',
        isActive: true,
      },
    ];

    for (const reward of rewardsFromPRD) {
      const existing = await ctx.db
        .query('rewards')
        .withIndex('by_client_id', (q) => q.eq('clientId', clientIdToUse))
        .filter((q) => q.eq(q.field('name'), reward.name))
        .first();

      if (!existing) {
        await ctx.db.insert('rewards', {
          clientId: clientIdToUse,
          ...reward,
        });
        console.log(`🎁 Seeded reward: ${reward.name}`);
      }
    }

    console.log('✅ Database seed process complete!');
  },
});
