import { cronJobs } from 'convex/server';
import { internal } from './_generated/api';

const crons = cronJobs();

// Mariana-Tek integration sync - runs every 5 minutes during business hours
// This polls for new check-in events and processes them automatically
crons.interval(
  'mariana-tek-sync',
  { minutes: 5 },
  internal.functions.marianaTekSync.syncMarianaTekCheckIns,
  {}
);

export default crons;
