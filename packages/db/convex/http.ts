import { httpRouter } from 'convex/server';
import { httpAction } from './_generated/server';
import { internal } from './_generated/api';
import { Webhook } from 'svix';
import type { WebhookEvent } from '@clerk/backend';

const http = httpRouter();

http.route({
  path: '/webhooks/clerk',
  method: 'POST',
  handler: httpAction(async (ctx, request) => {
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    if (!webhookSecret) {
      throw new Error('CLERK_WEBHOOK_SECRET is not set');
    }
    const svix_id = request.headers.get('svix-id');
    const svix_timestamp = request.headers.get('svix-timestamp');
    const svix_signature = request.headers.get('svix-signature');
    if (!svix_id || !svix_timestamp || !svix_signature) {
      return new Response('Error: Missing Svix headers', { status: 400 });
    }
    const body = await request.text();
    const wh = new Webhook(webhookSecret);
    let evt: WebhookEvent;
    try {
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      }) as WebhookEvent;
    } catch (err: any) {
      return new Response('Error verifying webhook', { status: 400 });
    }
    const eventType = evt.type;
    console.log(`🪝 Received Clerk webhook: ${eventType}`);

    switch (eventType) {
      case 'user.created':
      case 'user.updated': {
        const { id, first_name, last_name, email_addresses } = evt.data;
        await ctx.runMutation(
          internal.functions.users.internalStoreOrUpdateUser,
          {
            clerkUserId: id,
            firstName: first_name ?? undefined,
            lastName: last_name ?? undefined,
            email: email_addresses[0].email_address,
          }
        );
        break;
      }
      case 'organizationMembership.created':
      case 'organizationMembership.updated': {
        const { public_user_data, role } = evt.data;
        if (public_user_data?.user_id && role) {
          await ctx.runMutation(internal.functions.users.internalSetUserRole, {
            clerkUserId: public_user_data.user_id,
            role: role,
          });
        }
        break;
      }
    }

    return new Response(null, { status: 200 });
  }),
});

export default http;
