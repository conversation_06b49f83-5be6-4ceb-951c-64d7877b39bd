/**
 * Shared authentication helper functions for Convex functions.
 */

import { QueryCtx, MutationCtx } from '../_generated/server';
import { getUserByClerkId } from '../functions/users';
import { Doc } from '../_generated/dataModel';

/**
 * Gets the current authenticated user from the context.
 * @param {QueryCtx | MutationCtx} ctx - The Convex context
 * @returns {Promise<Doc<'users'> | null>} The authenticated user or null
 * @throws {Error} If authentication fails
 */
export async function getCurrentAuthenticatedUser(
  ctx: QueryCtx | MutationCtx
): Promise<Doc<'users'> | null> {
  try {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      return null;
    }

    const user = await getUserByClerkId(ctx, identity.subject);
    return user;
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    throw new Error('Authentication failed');
  }
}

/**
 * Requires an authenticated user and throws if not found.
 * @param {QueryCtx | MutationCtx} ctx - The Convex context
 * @returns {Promise<Doc<'users'>>} The authenticated user
 * @throws {Error} If user is not authenticated or not found
 */
export async function requireAuthenticatedUser(
  ctx: QueryCtx | MutationCtx
): Promise<Doc<'users'>> {
  const user = await getCurrentAuthenticatedUser(ctx);
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

/**
 * Requires a user with specific roles.
 * @param {QueryCtx | MutationCtx} ctx - The Convex context
 * @param {string[]} allowedRoles - Array of allowed roles
 * @returns {Promise<Doc<'users'>>} The authenticated user with valid role
 * @throws {Error} If user doesn't have required role
 */
export async function requireUserWithRole(
  ctx: QueryCtx | MutationCtx,
  allowedRoles: string[]
): Promise<Doc<'users'>> {
  const user = await requireAuthenticatedUser(ctx);
  
  const userRole = user.role ?? 'user';
  
  if (!allowedRoles.includes(userRole)) {
    throw new Error(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
  }
  
  return user;
}

/**
 * Requires an admin user (admin or staff role).
 * @param {QueryCtx | MutationCtx} ctx - The Convex context
 * @returns {Promise<Doc<'users'>>} The authenticated admin user
 * @throws {Error} If user is not an admin
 */
export async function requireAdminUser(
  ctx: QueryCtx | MutationCtx
): Promise<Doc<'users'>> {
  return requireUserWithRole(ctx, ['admin', 'staff']);
}

/**
 * Checks if a user belongs to a specific client.
 * @param {Doc<'users'>} user - The user to check
 * @param {string} clientId - The client ID to verify
 * @returns {boolean} True if user belongs to client
 */
export function userBelongsToClient(user: Doc<'users'>, clientId: string): boolean {
  return user.clientId === clientId;
}

/**
 * Requires a user to belong to a specific client.
 * @param {Doc<'users'>} user - The user to check
 * @param {string} clientId - The required client ID
 * @throws {Error} If user doesn't belong to client
 */
export function requireUserInClient(user: Doc<'users'>, clientId: string): void {
  if (!userBelongsToClient(user, clientId)) {
    throw new Error('Access denied. User not associated with this client.');
  }
}
