/**
 * @fileoverview
 * This file contains the logic for generating anonymous names for users.
 * It provides default lists of adjectives and nouns and a function to combine them.
 */

const defaultAdjectives = [
  'Aqua',
  'Amber',
  'Azure',
  'Abyssal',
  'Alpine',
  'Arctic',
  'Atomic',
  'Awesome',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  'Celes<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  'Chill',
  'Cobalt',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>smic',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  'Dandy',
  'Dapper',
  'Daring',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Du<PERSON><PERSON>',
  'Dynamic',
  'Eager',
  'Earth',
  'Ebony',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Exotic',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>lly',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Lava',
  'Lavender',
  'Lazy',
  '<PERSON>',
  '<PERSON>',
  'Light',
  '<PERSON>',
  '<PERSON>e',
  '<PERSON>',
  '<PERSON>d',
  '<PERSON>',
  '<PERSON>yal',
  '<PERSON>',
  '<PERSON>r',
  '<PERSON>',
  'Magenta',
  '<PERSON>',
  'Magma',
  'Majestic',
  '<PERSON>',
  'Mango',
  'Marine',
  'Maroon',
  'Master',
  'Mega',
  'Metal',
  'Midnight',
  'Mighty',
  'Mindful',
  'Mini',
  'Mint',
  'Misty',
  'Moon',
  'Mountain',
  'Mystic',
  'Navy',
  'Nebula',
  'Nether',
  'New',
  'Night',
  'Noble',
  'North',
  'Nova',
  'Obsidian',
  'Ocean',
  'Old',
  'Olive',
  'Omega',
  'Onyx',
  'Opal',
  'Orange',
  'Orchid',
  'Outer',
  'Pale',
  'Paper',
  'Peach',
  'Pearl',
  'Perfect',
  'Pepper',
  'Phantom',
  'Phoenix',
  'Pine',
  'Pink',
  'Plasma',
  'Platinum',
  'Plum',
  'Proud',
  'Psychic',
  'Purple',
  'Quasar',
  'Queen',
  'Quiet',
  'Radiant',
  'Rainbow',
  'Rapid',
  'Red',
  'Regal',
  'Retro',
  'Rising',
  'River',
  'Rogue',
  'Rose',
  'Royal',
  'Ruby',
  'Rusty',
  'Saffron',
  'Sand',
  'Sapphire',
  'Savage',
  'Scarlet',
  'Secret',
  'Senior',
  'Shadow',
  'Silent',
  'Silk',
  'Silver',
  'Sky',
  'Slate',
  'Small',
  'Smart',
  'Smoke',
  'Smooth',
  'Snow',
  'Solar',
  'Solid',
  'Space',
  'Spark',
  'Spirit',
  'Spooky',
  'Spring',
  'Star',
  'Steel',
  'Stealth',
  'Stone',
  'Storm',
  'Stray',
  'Strong',
  'Summer',
  'Sun',
  'Super',
  'Swift',
  'Tango',
  'Teal',
  'Terra',
  'Thunder',
  'Tidal',
  'Tiger',
  'Tiny',
  'Titan',
  'Topaz',
  'Toxic',
  'Tundra',
  'Turbo',
  'Turquoise',
  'Ultra',
  'Umber',
  'Urban',
  'Valiant',
  'Velvet',
  'Venom',
  'Verdant',
  'Violet',
  'Virtual',
  'Vivid',
  'Void',
  'Volcano',
  'Vortex',
  'Water',
  'West',
  'Whisper',
  'White',
  'Wild',
  'Wind',
  'Winter',
  'Wise',
  'Wonder',
  'Xeno',
  'Yellow',
  'Young',
  'Zen',
  'Zephyr',
  'Zero',
];

const defaultNouns = [
  'Acrobat',
  'Adventurer',
  'Agent',
  'Alchemist',
  'Ally',
  'Angel',
  'Archer',
  'Armor',
  'Arrow',
  'Artisan',
  'Assassin',
  'Avenger',
  'Avatar',
  'Bandit',
  'Barbarian',
  'Bard',
  'Baron',
  'Basilisk',
  'Battler',
  'Bear',
  'Beast',
  'Berserker',
  'Blade',
  'Blaze',
  'Bomber',
  'Brawler',
  'Bulldog',
  'Captain',
  'Captivator',
  'Cavalier',
  'Centaur',
  'Challenger',
  'Champion',
  'Charger',
  'Cheetah',
  'Climber',
  'Cobra',
  'Colossus',
  'Comet',
  'Commando',
  'Conqueror',
  'Courier',
  'Coyote',
  'Crafter',
  'Creator',
  'Creature',
  'Cruiser',
  'Crusader',
  'Cyclist',
  'Dancer',
  'Defender',
  'Demon',
  'Deserter',
  'Destroyer',
  'Detective',
  'Digger',
  'Dino',
  'Diver',
  'Dragon',
  'Dreamer',
  'Drifter',
  'Druid',
  'Duelist',
  'Eagle',
  'Echo',
  'Eclipse',
  'Elemental',
  'Elite',
  'Emissary',
  'Emperor',
  'Enchanter',
  'Engineer',
  'Enigma',
  'Entity',
  'Eon',
  'Explorer',
  'Falcon',
  'Fighter',
  'Figure',
  'Fisher',
  'Flame',
  'Flash',
  'Force',
  'Forger',
  'Fox',
  'Fragment',
  'Freak',
  'Friend',
  'Gadget',
  'Gambit',
  'Gambler',
  'Gardener',
  'Gargoyle',
  'Gem',
  'General',
  'Genie',
  'Genius',
  'Ghost',
  'Giant',
  'Gladiator',
  'Glimmer',
  'Gopher',
  'Gorilla',
  'Grave',
  'Grenade',
  'Griffin',
  'Guardian',
  'Gunner',
  'Guru',
  'Hacker',
  'Harbinger',
  'Hawk',
  'Healer',
  'Hero',
  'Hoax',
  'Hornet',
  'Hunter',
  'Hurricane',
  'Hydra',
  'Idol',
  'Illusion',
  'Inferno',
  'Infiltrator',
  'Innovator',
  'Inventor',
  'Jackal',
  'Jaguar',
  'Jester',
  'Jockey',
  'Joker',
  'Journey',
  'Judge',
  'Juggernaut',
  'Jumper',
  'King',
  'Knight',
  'Kraken',
  'Legend',
  'Leopard',
  'Leprechaun',
  'Librarian',
  'Lion',
  'Lizard',
  'Lurker',
  'Mage',
  'Magician',
  'Maker',
  'Mantis',
  'Marauder',
  'Marine',
  'Marksman',
  'Martial',
  'Marvel',
  'Master',
  'Maverick',
  'Mechanic',
  'Mercenary',
  'Meteor',
  'Mimic',
  'Miner',
  'Mirage',
  'Monarch',
  'Monk',
  'Monster',
  'Mutant',
  'Mystery',
  'Myth',
  'Navigator',
  'Necromancer',
  'Nemesis',
  'Ninja',
  'Nomad',
  'Nugget',
  'Nymph',
  'Oasis',
  'Observer',
  'Ogre',
  'Omega',
  'Operator',
  'Oracle',
  'Orc',
  'Outcast',
  'Outlaw',
  'Panda',
  'Panther',
  'Paragon',
  'Patriot',
  'Patroller',
  'Phantom',
  'Phoenix',
  'Pilot',
  'Pioneer',
  'Pirate',
  'Predator',
  'Priest',
  'Prince',
  'Prodigy',
  'Professor',
  'Prophet',
  'Prowler',
  'Punk',
  'Pyro',
  'Quake',
  'Quest',
  'Racer',
  'Raider',
  'Ranger',
  'Raptor',
  'Raven',
  'Reaper',
  'Rebel',
  'Recruit',
  'Rider',
  'Rifter',
  'Ringer',
  'Riot',
  'Rival',
  'Robot',
  'Rogue',
  'Runner',
  'Sabotage',
  'Sage',
  'Saint',
  'Samurai',
  'Savage',
  'Savior',
  'Scavenger',
  'Scholar',
  'Scorpion',
  'Scout',
  'Scribe',
  'Sculptor',
  'Seeker',
  'Sentinel',
  'Serpent',
  'Shade',
  'Shadow',
  'Shaman',
  'Shark',
  'Sheriff',
  'Shield',
  'Shifter',
  'Shooter',
  'Sidekick',
  'Siren',
  'Slayer',
  'Smasher',
  'Sniper',
  'Soldier',
  'Sorcerer',
  'Soul',
  'Spark',
  'Specter',
  'Spider',
  'Spike',
  'Spirit',
  'Spy',
  'Stalker',
  'Star',
  'Storm',
  'Stranger',
  'Striker',
  'Summoner',
  'Survivor',
  'Tank',
  'Teacher',
  'Techno',
  'Tempest',
  'Thief',
  'Thorn',
  'Thunder',
  'Tiger',
  'Titan',
  'Tracer',
  'Tracker',
  'Trader',
  'Traveler',
  'Trickster',
  'Troll',
  'Trooper',
  'Tyrant',
  'Undead',
  'Vagrant',
  'Valkyrie',
  'Vampire',
  'Vandal',
  'Vanguard',
  'Vapor',
  'Vector',
  'Vindicator',
  'Viper',
  'Virtuoso',
  'Vision',
  'Visitor',
  'Voyager',
  'Vulture',
  'Warden',
  'Warlock',
  'Warlord',
  'Warrior',
  'Watcher',
  'Werewolf',
  'Whirlwind',
  'Whisper',
  'Witch',
  'Wizard',
  'Wolf',
  'Wolverine',
  'Wonder',
  'Wraith',
  'Wrangler',
  'Wreckage',
  'Xenon',
  'Yeoman',
  'Yeti',
  'Zealot',
  'Zenith',
  'Zephyr',
  'Zombie',
  'Zodiac',
];

/**
 * Generates a random anonymous name.
 * @param {string[]} [adjectives=defaultAdjectives] - An array of adjectives to use.
 * @param {string[]} [nouns=defaultNouns] - An array of nouns to use.
 * @returns {string} A randomly generated name in the format "Adjective Noun".
 */
export function generateAnonymousName(
  adjectives: string[] = defaultAdjectives,
  nouns: string[] = defaultNouns
): string {
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  return `${adjective} ${noun}`;
}
