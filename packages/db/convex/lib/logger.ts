/**
 * Centralized logging utility for Convex functions.
 * Provides structured logging with different levels and contexts.
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  userId?: string;
  functionName?: string;
  clientId?: string;
  [key: string]: any;
}

interface LogEntry {
  level: LogLevel;
  message: string;
  context?: LogContext;
  timestamp: number;
  error?: Error;
}

/**
 * Logger class for structured logging in Convex functions.
 */
export class Logger {
  private context: LogContext;

  constructor(context: LogContext = {}) {
    this.context = context;
  }

  /**
   * Creates a new logger instance with additional context.
   * @param {LogContext} additionalContext - Additional context to merge
   * @returns {Logger} New logger instance
   */
  withContext(additionalContext: LogContext): Logger {
    return new Logger({ ...this.context, ...additionalContext });
  }

  /**
   * Logs a debug message.
   * @param {string} message - The log message
   * @param {LogContext} [context] - Additional context
   */
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  /**
   * Logs an info message.
   * @param {string} message - The log message
   * @param {LogContext} [context] - Additional context
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * Logs a warning message.
   * @param {string} message - The log message
   * @param {LogContext} [context] - Additional context
   */
  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  /**
   * Logs an error message.
   * @param {string} message - The log message
   * @param {Error | LogContext} [errorOrContext] - Error object or additional context
   * @param {LogContext} [context] - Additional context if first param is Error
   */
  error(
    message: string,
    errorOrContext?: Error | LogContext,
    context?: LogContext
  ): void {
    let error: Error | undefined;
    let finalContext: LogContext | undefined;

    if (errorOrContext instanceof Error) {
      error = errorOrContext;
      finalContext = context;
    } else {
      finalContext = errorOrContext;
    }

    this.log('error', message, finalContext, error);
  }

  /**
   * Internal logging method.
   * @param {LogLevel} level - The log level
   * @param {string} message - The log message
   * @param {LogContext} [context] - Additional context
   * @param {Error} [error] - Error object
   */
  private log(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error
  ): void {
    const logEntry: LogEntry = {
      level,
      message,
      context: { ...this.context, ...context },
      timestamp: Date.now(),
      error,
    };

    // In development, use console methods for better formatting
    if (process.env.NODE_ENV === 'development') {
      const contextStr = Object.keys(logEntry.context || {}).length > 0
        ? ` ${JSON.stringify(logEntry.context)}`
        : '';
      
      const fullMessage = `[${level.toUpperCase()}] ${message}${contextStr}`;

      switch (level) {
        case 'debug':
          console.debug(fullMessage, error);
          break;
        case 'info':
          console.info(fullMessage, error);
          break;
        case 'warn':
          console.warn(fullMessage, error);
          break;
        case 'error':
          console.error(fullMessage, error);
          break;
      }
    } else {
      // In production, use structured logging
      console.log(JSON.stringify(logEntry));
    }
  }
}

/**
 * Creates a logger instance for a specific function.
 * @param {string} functionName - The name of the function
 * @param {LogContext} [context] - Additional context
 * @returns {Logger} Logger instance
 */
export function createLogger(functionName: string, context?: LogContext): Logger {
  return new Logger({ functionName, ...context });
}

/**
 * Default logger instance for general use.
 */
export const logger = new Logger();

/**
 * Performance timing utility for logging function execution times.
 */
export class PerformanceTimer {
  private startTime: number;
  private logger: Logger;
  private operation: string;

  constructor(logger: Logger, operation: string) {
    this.logger = logger;
    this.operation = operation;
    this.startTime = Date.now();
    this.logger.debug(`Starting ${operation}`);
  }

  /**
   * Ends the timer and logs the duration.
   * @param {LogContext} [context] - Additional context
   */
  end(context?: LogContext): void {
    const duration = Date.now() - this.startTime;
    this.logger.info(`Completed ${this.operation}`, {
      duration: `${duration}ms`,
      ...context,
    });
  }
}

/**
 * Creates a performance timer for measuring operation duration.
 * @param {Logger} logger - Logger instance
 * @param {string} operation - Operation name
 * @returns {PerformanceTimer} Timer instance
 */
export function createTimer(logger: Logger, operation: string): PerformanceTimer {
  return new PerformanceTimer(logger, operation);
}
