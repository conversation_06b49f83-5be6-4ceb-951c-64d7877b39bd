/**
 * MarianaTekService - Integration service for Mariana-Tek API
 * 
 * Handles polling for check-in events and user data synchronization.
 * Since Mariana-Tek doesn't provide real-time webhooks for check-ins,
 * this service implements a polling-based approach.
 */

export interface MarianaTekUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

export interface MarianaTekReservation {
  id: string;
  user: MarianaTekUser;
  class_session: {
    id: string;
    name: string;
    start_time: string;
    class_session_type: {
      name: string;
    };
  };
  checked_in_at?: string;
  status: string;
}

export interface CheckInEvent {
  reservationId: string;
  userId: string;
  userEmail: string;
  userName?: string;
  classType: string;
  checkedInAt: Date;
  externalTimestamp: number;
}

export class MarianaTekService {
  private apiKey: string;
  private baseUrl: string = 'https://api.marianatek.com';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Test API connection and credentials
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/users/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/vnd.api+json',
        },
      });

      if (response.ok) {
        return { success: true };
      } else {
        const errorText = await response.text();
        return { 
          success: false, 
          error: `API test failed: ${response.status} - ${errorText}` 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Get recent check-ins since the last sync
   * Uses polling approach to fetch reservations with recent check-ins
   */
  async getRecentCheckIns(since: Date): Promise<CheckInEvent[]> {
    try {
      // Format date for API query (ISO string)
      const sinceIso = since.toISOString();
      
      // Query reservations that have been checked in since the last sync
      const url = new URL(`${this.baseUrl}/reservations/`);
      url.searchParams.set('checked_in_at__gte', sinceIso);
      url.searchParams.set('status', 'checked_in');
      url.searchParams.set('page_size', '100'); // Adjust based on expected volume

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/vnd.api+json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} - ${await response.text()}`);
      }

      const data = await response.json();
      return this.parseCheckInEvents(data);
    } catch (error) {
      console.error('Failed to fetch recent check-ins:', error);
      throw error;
    }
  }

  /**
   * Parse API response into CheckInEvent objects
   */
  private parseCheckInEvents(apiResponse: any): CheckInEvent[] {
    const events: CheckInEvent[] = [];
    
    if (!apiResponse.results || !Array.isArray(apiResponse.results)) {
      return events;
    }

    for (const reservation of apiResponse.results) {
      try {
        if (reservation.checked_in_at && reservation.user && reservation.class_session) {
          const checkedInAt = new Date(reservation.checked_in_at);
          
          events.push({
            reservationId: reservation.id,
            userId: reservation.user.id,
            userEmail: reservation.user.email,
            userName: reservation.user.first_name && reservation.user.last_name 
              ? `${reservation.user.first_name} ${reservation.user.last_name}`
              : undefined,
            classType: reservation.class_session.class_session_type?.name || 'Unknown Class',
            checkedInAt,
            externalTimestamp: checkedInAt.getTime(),
          });
        }
      } catch (error) {
        console.warn('Failed to parse reservation:', reservation, error);
        // Continue processing other reservations
      }
    }

    return events;
  }

  /**
   * Get user details by ID for user matching
   */
  async getUserById(userId: string): Promise<MarianaTekUser | null> {
    try {
      const response = await fetch(`${this.baseUrl}/users/${userId}/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/vnd.api+json',
        },
      });

      if (response.ok) {
        return await response.json();
      } else if (response.status === 404) {
        return null;
      } else {
        throw new Error(`Failed to fetch user: ${response.status}`);
      }
    } catch (error) {
      console.error(`Failed to fetch user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Search for users by email for user matching
   */
  async searchUsersByEmail(email: string): Promise<MarianaTekUser[]> {
    try {
      const url = new URL(`${this.baseUrl}/users/`);
      url.searchParams.set('email', email);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/vnd.api+json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        return data.results || [];
      } else {
        throw new Error(`User search failed: ${response.status}`);
      }
    } catch (error) {
      console.error(`Failed to search users by email ${email}:`, error);
      return [];
    }
  }
}

/**
 * Utility function to create service instance with encrypted credentials
 */
export function createMarianaTekService(encryptedApiKey: string): MarianaTekService {
  // In a real implementation, you would decrypt the API key here
  // For now, we'll assume the key is already decrypted
  const apiKey = encryptedApiKey; // TODO: Implement decryption
  return new MarianaTekService(apiKey);
}
