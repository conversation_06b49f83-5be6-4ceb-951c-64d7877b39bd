import { z } from 'zod';

export const activityTypeArgs = {
  name: z.string().min(1, 'Name cannot be empty'),
  key: z
    .string()
    .min(1, 'Key cannot be empty')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Key must be in kebab-case and contain only lowercase letters, numbers, and hyphens.'
    ),
  points: z.number().positive('Points must be a positive number'),
  iconName: z.string().min(1, 'Icon name cannot be empty'),
};

export const activityTypeCreateSchema = z.object(activityTypeArgs);

export const activityTypeUpdateSchema = z.object({
  id: z.string(),
  ...activityTypeArgs,
});

export type ActivityTypeCreate = z.infer<typeof activityTypeCreateSchema>;
export type ActivityTypeUpdate = z.infer<typeof activityTypeUpdateSchema>;
