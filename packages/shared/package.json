{"name": "@fitness-rewards/shared", "private": true, "version": "0.1.0", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./schemas/activityTypeSchemas": {"types": "./dist/activityTypeSchemas.d.ts", "import": "./dist/activityTypeSchemas.js"}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "devDependencies": {"tsup": "^8.2.3", "typescript": "^5.4.5", "zod": "^3.23.8"}, "dependencies": {"zod": "^3.23.8"}}