import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  // Add this line to tell Vite to look for the .env file
  // in the monorepo root directory (two levels up from `apps/web`).
  envDir: '../../',

  plugins: [react(), tsconfigPaths()],
  server: {
    port: 3000,
  },
  build: {
    outDir: 'dist',
  },
});
