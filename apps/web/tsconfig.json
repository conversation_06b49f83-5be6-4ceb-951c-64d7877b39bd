{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "types": ["vite/client"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../../packages/shared/src/*"], "@ui/*": ["../../packages/ui/src/*"], "@db": ["../../packages/db/convex/_generated/api"], "@db/types": ["../../packages/db/convex/_generated/dataModel"], "@fitness-rewards/core/*": ["../../packages/core/src/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}