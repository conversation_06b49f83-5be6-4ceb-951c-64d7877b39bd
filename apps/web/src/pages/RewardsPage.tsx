import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@db';
import { WithLoading } from '../components/utils/WithLoading';
import { RewardsList } from '../components/rewards/RewardsList';
import { RedemptionHistory } from '../components/rewards/RedemptionHistory';
import { AlertTriangle } from 'lucide-react';

const RewardsPage = () => {
  const [currentView, setCurrentView] = useState<'catalog' | 'history'>(
    'catalog'
  );
  const user = useQuery(api.functions.users.getCurrentUser);
  const rewards = useQuery(api.functions.rewards.getActive);

  const points = user?.points ?? 0;

  return (
    <div className="container mx-auto max-w-7xl px-4 py-8">
      <div className="flex flex-col items-center justify-between rounded-lg bg-white p-6 shadow-md sm:flex-row">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Rewards Catalog</h1>
          <p className="mt-1 text-gray-600">
            Spend your hard-earned points on exclusive rewards.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-500">Your Points</div>
            <div className="text-4xl font-bold text-indigo-600">{points}</div>
          </div>
        </div>
      </div>

      <div className="mt-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setCurrentView('catalog')}
            className={`whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium ${
              currentView === 'catalog'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            Browse Rewards
          </button>
          <button
            onClick={() => setCurrentView('history')}
            className={`whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium ${
              currentView === 'history'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
            aria-current={currentView === 'history' ? 'page' : undefined}
          >
            My Redemptions
          </button>
        </nav>
      </div>

      <div className="mt-8">
        <WithLoading isPending={rewards === undefined || user === undefined}>
          {rewards && user ? (
            currentView === 'catalog' ? (
              <RewardsList rewards={rewards} userPoints={user.points} />
            ) : (
              <RedemptionHistory />
            )
          ) : (
            <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
              <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                Could not load rewards
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Please check your connection and try again.
              </p>
            </div>
          )}
        </WithLoading>
      </div>
    </div>
  );
};

export default RewardsPage;
