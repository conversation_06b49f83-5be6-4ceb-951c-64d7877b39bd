import { ReactNode } from 'react';
import { NavLink, Outlet } from 'react-router-dom';
import { HugeIcon } from '../../components/icons/HugeIcon';

const historyTabs = [
  {
    id: 'activities',
    label: 'Activity Feed',
    href: '/history/activities',
    icon: 'activity-02',
  },
  {
    id: 'achievements',
    label: 'My Milestones',
    href: '/history/milestones',
    icon: 'flag-01',
  },
];

/**
 * Provides a consistent layout with tab navigation for the user history pages.
 * @returns {JSX.Element} The history layout component.
 */
export default function HistoryLayout() {
  return (
    <div className="mx-auto max-w-5xl">
      <header className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">
          Your History
        </h1>
        <p className="mt-2 text-lg text-gray-600">
          A complete record of your journey on the platform. Review your
          activities and celebrate your achievements.
        </p>
      </header>

      <div className="mb-8 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6" aria-label="Tabs">
          {historyTabs.map((tab) => (
            <NavLink
              key={tab.id}
              to={tab.href}
              className={({ isActive }) =>
                `flex items-center space-x-3 border-b-2 px-3 py-2 text-sm font-medium transition-colors
                ${
                  isActive
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }`
              }
            >
              <HugeIcon icon={tab.icon} className="h-5 w-5" />
              <span>{tab.label}</span>
            </NavLink>
          ))}
        </nav>
      </div>

      <main>
        <Outlet />
      </main>
    </div>
  );
}
