import { useQuery } from 'convex/react';
import { api } from '@db';
import { MilestoneCard } from '../../components/history/MilestoneCard';
import { Spinner } from '../../components/ui/Spinner';
import { Trophy } from 'lucide-react';

/**
 * Renders the user's milestones history page, displaying a gallery of unlocked milestones.
 *
 * @returns {JSX.Element} The milestones history page component.
 */
export default function MilestonesHistoryPage() {
  const milestones = useQuery(api.functions.history.getAchievementsHistory);
  const isLoading = milestones === undefined;

  if (isLoading) {
    return <Spinner size="lg" />;
  }

  if (milestones.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
        <Trophy className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">
          No Milestones Yet
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          Keep participating to unlock milestones and see them here!
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      {milestones.map((milestone) => (
        <MilestoneCard key={milestone._id} milestone={milestone} />
      ))}
    </div>
  );
}
