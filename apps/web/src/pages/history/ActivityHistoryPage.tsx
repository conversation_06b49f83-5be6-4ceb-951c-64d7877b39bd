import { usePaginatedQuery } from 'convex/react';
import { api } from '@db';
import { Button } from '../../components/ui/button';
import { ActivityListItem } from '../../components/history/ActivityListItem';
import { Spinner } from '../../components/ui/Spinner';
import { History } from 'lucide-react';

/**
 * Renders the user's activity history page.
 * Fetches activities using a paginated query and displays them in a list.
 * Provides a "Load More" button to fetch additional activities.
 *
 * @returns {JSX.Element} The activity history page component.
 */
export default function ActivityHistoryPage() {
  const {
    results: activities,
    status,
    loadMore,
  } = usePaginatedQuery(
    api.functions.history.getActivityHistory,
    {},
    { initialNumItems: 25 }
  );

  const isLoading = status === 'loading';

  if (isLoading && activities.length === 0) {
    return <Spinner size="lg" />;
  }

  if (!isLoading && activities.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
        <History className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">
          No Activity Yet
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          You haven't performed any activities yet. Get started to see your
          history here!
        </p>
      </div>
    );
  }

  return (
    <div>
      <ul className="space-y-4">
        {activities.map((item) => (
          <ActivityListItem key={item._id} item={item} />
        ))}
      </ul>
      {status !== 'exhausted' && (
        <div className="mt-8 flex justify-center">
          <Button
            onClick={() => loadMore(10)}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}
