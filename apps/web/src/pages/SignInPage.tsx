import { SignIn } from '@clerk/clerk-react';
import { useLocation } from 'react-router-dom';

/**
 * Sign-in page component that handles redirecting users back to their intended destination
 * after successful authentication.
 */
export default function SignInPage() {
  const location = useLocation();

  // Get the redirect URL from either location state or URL params
  const urlParams = new URLSearchParams(location.search);
  const redirectFromParams = urlParams.get('redirect');
  const redirectFromState = location.state?.from?.pathname;
  const redirectUrl = redirectFromParams || redirectFromState || '/dashboard';

  // Construct sign-up URL with state to preserve the redirect destination
  const signUpUrl =
    redirectFromParams || redirectFromState
      ? `/sign-up?redirect=${encodeURIComponent(redirectUrl)}`
      : '/sign-up';

  return (
    <div className="flex items-center justify-center py-12">
      <SignIn redirectUrl={redirectUrl} signUpUrl={signUpUrl} />
    </div>
  );
}
