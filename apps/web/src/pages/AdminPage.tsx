import { useState, lazy, Suspense } from 'react';
import { HugeIcon } from '@/components/icons/HugeIcon';
// Commenting out for now, will be created in the next step
// import ActivitiesManagementTab from '@/components/admin/ActivitiesManagementTab';

const RewardsManagementTab = lazy(
  () => import('../components/admin/RewardsManagementTab')
);
const RedemptionFulfillmentTab = lazy(
  () => import('../components/admin/RedemptionFulfillmentTab')
);
const MilestonesManagementTab = lazy(
  () => import('../components/admin/MilestonesManagementTab')
);
const SettingsTab = lazy(() => import('@/components/admin/SettingsTab'));
const ActivitiesManagementTab = lazy(
  () => import('../components/admin/ActivitiesManagementTab')
);
const IntegrationsTab = lazy(
  () => import('../components/admin/IntegrationsTab')
);

const TABS = {
  rewards: {
    label: 'Rewards',
    icon: 'gift',
  },
  milestones: {
    label: 'Milestones',
    icon: 'star',
  },
  activities: {
    label: 'Activities',
    icon: 'check-list',
  },
  integrations: {
    label: 'Integrations',
    icon: 'link-02',
  },
  redemptions: {
    label: 'Fulfillment',
    icon: 'user-group',
  },
  settings: {
    label: 'Settings',
    icon: 'settings-02',
  },
};

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<keyof typeof TABS>('rewards');

  return (
    <div className="container mx-auto px-4 py-8">
      <div>
        <h1 className="text-3xl font-bold">Client Admin Dashboard</h1>
        <p className="text-gray-600">
          Manage your rewards, milestones, activities, and member redemptions.
        </p>
      </div>
      <div className="mt-8">
        <div className="border-b border-gray-200">
          <nav
            className="-mb-px flex space-x-6 overflow-x-auto"
            aria-label="Tabs"
          >
            {Object.entries(TABS).map(([key, tab]) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as keyof typeof TABS)}
                className={`${
                  activeTab === key
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                } flex items-center gap-2 whitespace-nowrap border-b-2 px-3 py-4 text-sm font-medium transition-colors`}
              >
                <HugeIcon icon={tab.icon} className="h-5 w-5" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        <div className="mt-6">
          <Suspense fallback={<div>Loading...</div>}>
            {activeTab === 'rewards' && <RewardsManagementTab />}
            {activeTab === 'milestones' && <MilestonesManagementTab />}
            {activeTab === 'activities' && <ActivitiesManagementTab />}
            {activeTab === 'integrations' && <IntegrationsTab />}
            {activeTab === 'redemptions' && <RedemptionFulfillmentTab />}
            {activeTab === 'settings' && <SettingsTab />}
          </Suspense>
        </div>
      </div>
    </div>
  );
}
