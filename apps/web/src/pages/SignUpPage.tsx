import { SignUp } from '@clerk/clerk-react';
import { useLocation } from 'react-router-dom';

/**
 * Sign-up page component that handles redirecting users back to their intended destination
 * after successful registration and authentication.
 */
export default function SignUpPage() {
  const location = useLocation();

  // Get the redirect URL from either location state or URL params
  const urlParams = new URLSearchParams(location.search);
  const redirectFromParams = urlParams.get('redirect');
  const redirectFromState = location.state?.from?.pathname;
  const redirectUrl = redirectFromParams || redirectFromState || '/dashboard';

  // Construct sign-in URL with state to preserve the redirect destination
  const signInUrl =
    redirectFromParams || redirectFromState
      ? `/sign-in?redirect=${encodeURIComponent(redirectUrl)}`
      : '/sign-in';

  return (
    <div className="flex justify-center py-12">
      <SignUp redirectUrl={redirectUrl} signInUrl={signInUrl} />
    </div>
  );
}
