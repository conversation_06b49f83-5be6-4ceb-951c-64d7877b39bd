import { useMutation, useQuery } from 'convex/react';
import { api } from '@db';
import { WithLoading } from '../components/utils/WithLoading';
import { UserAvatar } from '../components/user/UserAvatar';
import { Award, Heart } from 'lucide-react';
import { Doc, Id } from '@db/types';
import toast from 'react-hot-toast';

/**
 * A button for cheering a user on the leaderboard.
 * Displays cheer count and handles sending cheers.
 */
const CheerButton = ({
  cheeredUserId,
  cheerData,
  isCurrentUser,
}: {
  cheeredUserId: Id<'users'>;
  cheerData: { count: number; cheeredByCurrentUser: boolean } | undefined;
  isCurrentUser: boolean;
}) => {
  const sendCheer = useMutation(api.functions.cheers.send);

  const handleCheer = () => {
    if (isCurrentUser) return;

    toast.promise(sendCheer({ cheeredUserId }), {
      loading: 'Sending cheer...',
      success: 'Cheer sent!',
      error: (err) => err.data?.message || 'Failed to send cheer.',
    });
  };

  const isDisabled = isCurrentUser || cheerData?.cheeredByCurrentUser;

  return (
    <button
      onClick={handleCheer}
      disabled={isDisabled}
      className={`flex items-center gap-1.5 rounded-full px-3 py-1 text-sm transition-colors ${
        isDisabled
          ? 'cursor-not-allowed bg-gray-200 text-gray-500'
          : 'bg-pink-100 text-pink-700 hover:bg-pink-200'
      } ${
        cheerData?.cheeredByCurrentUser
          ? 'border-2 border-pink-500 font-semibold'
          : ''
      }`}
    >
      <Heart
        className={`h-4 w-4 ${
          cheerData?.cheeredByCurrentUser ? 'fill-current' : ''
        }`}
      />
      <span>{cheerData?.count ?? 0}</span>
    </button>
  );
};

/**
 * A single row in the leaderboard list.
 * @param {object} props - Component props.
 * @param {Doc<'users'> & { monthlyPoints: number }} props.user - The user data for the row.
 * @param {number} props.rank - The user's rank.
 * @param {boolean} [props.isCurrentUser=false] - Whether this row is for the current user.
 * @param {{ count: number, cheeredByCurrentUser: boolean } | undefined} props.cheerData - Cheer data for the user.
 * @returns {JSX.Element} The leaderboard row component.
 */
const LeaderboardRow = ({
  user,
  rank,
  isCurrentUser = false,
  cheerData,
}: {
  user: Doc<'users'> & { monthlyPoints?: number };
  rank: number;
  isCurrentUser?: boolean;
  cheerData: { count: number; cheeredByCurrentUser: boolean } | undefined;
}) => {
  const displayName =
    user.leaderboardSettings?.showRealName === true
      ? `${user.firstName} ${user.lastName}`
      : (user.leaderboardSettings?.anonymousName ?? 'Anonymous User');

  const getRankIcon = () => {
    if (rank === 1) return <span className="text-2xl text-yellow-400">🥇</span>;
    if (rank === 2) return <span className="text-2xl text-gray-400">🥈</span>;
    if (rank === 3) return <span className="text-2xl text-yellow-600">🥉</span>;
    return (
      <span className="font-bold text-gray-600">{rank.toLocaleString()}</span>
    );
  };

  return (
    <div
      className={`flex items-center space-x-4 rounded-lg p-3 transition-all ${
        isCurrentUser
          ? 'scale-105 transform bg-indigo-100 ring-2 ring-indigo-500'
          : 'bg-white'
      }`}
    >
      <div className="w-10 text-center">{getRankIcon()}</div>
      <UserAvatar user={user} className="h-12 w-12 flex-shrink-0" />
      <div className="flex-grow">
        <p className="font-semibold text-gray-800">{displayName}</p>
        <p className="text-sm text-gray-500">
          {(user.monthlyPoints ?? user.points).toLocaleString()} points this
          month
        </p>
      </div>
      <CheerButton
        cheeredUserId={user._id}
        cheerData={cheerData}
        isCurrentUser={isCurrentUser}
      />
    </div>
  );
};

/**
 * Renders the main leaderboard page, displaying top users by points.
 * @returns {JSX.Element} The leaderboard page component.
 */
export default function LeaderboardPage() {
  const leaderboard = useQuery(api.functions.users.getLeaderboard);
  const currentUser = useQuery(api.functions.users.getCurrentUser);

  const leaderboardUserIds = leaderboard?.map((u) => u._id) ?? [];
  const cheerData = useQuery(
    api.functions.cheers.get,
    leaderboardUserIds.length > 0 ? { userIds: leaderboardUserIds } : 'skip'
  );

  const currentUserOnBoard =
    currentUser && leaderboard
      ? leaderboard.find((u) => u._id === currentUser._id)
      : undefined;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="flex items-center justify-center gap-3 text-4xl font-bold text-gray-800">
          <Award className="h-10 w-10 text-indigo-600" />
          Leaderboard
        </h1>
        <p className="mt-2 text-lg text-gray-600">
          See who's leading the pack this month!
        </p>
      </div>

      <WithLoading
        isPending={leaderboard === undefined || cheerData === undefined}
      >
        {leaderboard && leaderboard.length > 0 ? (
          <div className="mx-auto max-w-2xl space-y-3">
            {leaderboard.map((user, index) => (
              <LeaderboardRow
                key={user._id}
                user={user}
                rank={index + 1}
                isCurrentUser={user._id === currentUser?._id}
                cheerData={cheerData ? cheerData[user._id] : undefined}
              />
            ))}

            {/* If current user is not in the top list, show their rank separately */}
            {currentUser && !currentUserOnBoard && (
              <>
                <div className="my-4 flex items-center justify-center space-x-2">
                  <div className="h-px flex-grow bg-gray-300"></div>
                  <span className="text-sm text-gray-500">...</span>
                  <div className="h-px flex-grow bg-gray-300"></div>
                </div>
                <p className="text-center text-gray-600">
                  You are not yet on the monthly leaderboard.
                </p>
              </>
            )}
          </div>
        ) : (
          <div className="py-16 text-center">
            <p className="text-xl text-gray-500">The leaderboard is empty.</p>
            <p className="mt-2 text-sm text-gray-400">
              Start logging activities to get on the board!
            </p>
          </div>
        )}
      </WithLoading>
    </div>
  );
}
