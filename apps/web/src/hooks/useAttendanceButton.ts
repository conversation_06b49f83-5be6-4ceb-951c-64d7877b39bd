/**
 * Custom hook for managing class attendance button state and logic.
 */

import { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { toast } from 'react-hot-toast';

export type ButtonState = 'default' | 'loading' | 'success' | 'error' | 'cooldown';

interface AttendanceResult {
  totalPoints: number;
  newMilestones: Array<{ name: string }>;
  tierAdvancement?: {
    newTier: string;
  };
}

interface UseAttendanceButtonReturn {
  buttonState: ButtonState;
  errorMessage: string;
  handleClick: () => Promise<void>;
  isDisabled: boolean;
}

/**
 * Hook for managing class attendance button functionality.
 * Handles all state transitions, API calls, and user feedback.
 * @returns {UseAttendanceButtonReturn} Button state and handlers
 */
export function useAttendanceButton(): UseAttendanceButtonReturn {
  const [buttonState, setButtonState] = useState<ButtonState>('default');
  const [errorMessage, setErrorMessage] = useState('');

  const logAttendance = useMutation(api.functions.activities.logClassAttendance);

  // Reset error state after 5 seconds
  useEffect(() => {
    if (buttonState === 'error') {
      const timer = setTimeout(() => {
        setButtonState('default');
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [buttonState]);

  // Handle success state and cooldown
  useEffect(() => {
    if (buttonState === 'success') {
      const successTimer = setTimeout(() => {
        setButtonState('cooldown');

        // Enable button after cooldown period (1 minute)
        const cooldownTimer = setTimeout(() => {
          setButtonState('default');
        }, 60000); // 1 minute cooldown

        return () => clearTimeout(cooldownTimer);
      }, 2000);

      return () => clearTimeout(successTimer);
    }
  }, [buttonState]);

  /**
   * Handles the attendance logging process.
   */
  const handleClick = async (): Promise<void> => {
    if (buttonState === 'loading' || buttonState === 'cooldown') {
      return;
    }

    setButtonState('loading');
    setErrorMessage('');

    try {
      const result = await logAttendance() as AttendanceResult;
      setButtonState('success');

      // Show toast notification for points earned
      toast.success(`You earned ${result.totalPoints} points!`);

      // Show milestone achievement notifications
      if (result.newMilestones.length > 0) {
        result.newMilestones.forEach((milestone) => {
          toast.success(`🏆 Achievement Unlocked: ${milestone.name}`, {
            duration: 5000,
            icon: '🏆',
          });
        });
      }

      // Show tier advancement notification
      if (result.tierAdvancement) {
        toast.success(
          `🎉 Tier Advanced! You've reached ${result.tierAdvancement.newTier} tier!`,
          {
            duration: 6000,
            icon: '🎉',
          }
        );
      }
    } catch (error) {
      setButtonState('error');
      const message = error instanceof Error ? error.message : 'An error occurred';
      setErrorMessage(message);
      console.error('Failed to log attendance:', error);
    }
  };

  const isDisabled = buttonState === 'loading' || buttonState === 'cooldown';

  return {
    buttonState,
    errorMessage,
    handleClick,
    isDisabled,
  };
}
