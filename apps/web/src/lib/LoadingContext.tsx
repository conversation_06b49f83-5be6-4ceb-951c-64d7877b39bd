import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
  useEffect,
} from 'react';

interface LoadingContextType {
  startLoading: (id: string) => void;
  finishLoading: (id: string) => void;
  isLoading: boolean;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [loadingSet, setLoadingSet] = useState<Set<string>>(new Set());

  const startLoading = useCallback((id: string) => {
    setLoadingSet((prev) => {
      const newSet = new Set(prev);
      newSet.add(id);
      return newSet;
    });
  }, []);

  const finishLoading = useCallback((id: string) => {
    setLoadingSet((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  const isLoading = loadingSet.size > 0;

  return (
    <LoadingContext.Provider value={{ startLoading, finishLoading, isLoading }}>
      {children}
    </LoadingContext.Provider>
  );
};

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}
