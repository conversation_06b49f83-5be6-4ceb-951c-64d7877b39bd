/**
 * Input validation schemas using Zod for the web application.
 */

import { z } from 'zod';

/**
 * Schema for validating email addresses.
 */
export const emailSchema = z
  .string()
  .email('Please enter a valid email address')
  .min(1, 'Email is required');

/**
 * Schema for validating user names.
 */
export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes');

/**
 * Schema for validating reward creation/editing.
 */
export const rewardSchema = z.object({
  name: z
    .string()
    .min(1, 'Reward name is required')
    .max(100, 'Reward name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  cost: z
    .number()
    .min(1, 'Cost must be at least 1 point')
    .max(10000, 'Cost cannot exceed 10,000 points'),
  imageUrl: z
    .string()
    .url('Please enter a valid URL')
    .optional()
    .or(z.literal('')),
  isActive: z.boolean(),
});

/**
 * Schema for validating milestone creation/editing.
 */
export const milestoneSchema = z.object({
  name: z
    .string()
    .min(1, 'Milestone name is required')
    .max(100, 'Milestone name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  triggerType: z.literal('activity_count'),
  conditions: z.object({
    activityTypeMatcher: z
      .string()
      .min(1, 'Activity type is required'),
    countThreshold: z
      .number()
      .min(1, 'Count threshold must be at least 1')
      .max(1000, 'Count threshold cannot exceed 1,000'),
  }),
  rewards: z
    .array(
      z.object({
        type: z.enum(['points', 'badge']),
        value: z.union([z.string(), z.number()]),
      })
    )
    .min(1, 'At least one reward is required'),
  isEnabled: z.boolean(),
  isRepeatable: z.boolean(),
});

/**
 * Schema for validating search queries.
 */
export const searchQuerySchema = z
  .string()
  .min(2, 'Search query must be at least 2 characters')
  .max(100, 'Search query must be less than 100 characters')
  .regex(/^[a-zA-Z0-9\s@.-]+$/, 'Search query contains invalid characters');

/**
 * Schema for validating user role updates.
 */
export const userRoleSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  role: z.enum(['user', 'staff', 'admin'], {
    errorMap: () => ({ message: 'Role must be user, staff, or admin' }),
  }),
});

/**
 * Schema for validating pagination parameters.
 */
export const paginationSchema = z.object({
  page: z
    .number()
    .min(1, 'Page must be at least 1')
    .max(1000, 'Page cannot exceed 1,000')
    .default(1),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .default(20),
});

/**
 * Schema for validating filter parameters.
 */
export const filterSchema = z.object({
  status: z.enum(['active', 'inactive', 'all']).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  category: z.string().optional(),
});

/**
 * Type exports for use in components.
 */
export type RewardInput = z.infer<typeof rewardSchema>;
export type MilestoneInput = z.infer<typeof milestoneSchema>;
export type SearchQuery = z.infer<typeof searchQuerySchema>;
export type UserRoleUpdate = z.infer<typeof userRoleSchema>;
export type PaginationParams = z.infer<typeof paginationSchema>;
export type FilterParams = z.infer<typeof filterSchema>;

/**
 * Utility function to safely parse and validate data.
 * @param {z.ZodSchema} schema - The Zod schema to validate against
 * @param {unknown} data - The data to validate
 * @returns {Object} Validation result with success flag and data or errors
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map((err) => err.message);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

/**
 * Utility function to get field-specific errors from Zod validation.
 * @param {z.ZodError} error - The Zod error object
 * @returns {Record<string, string>} Object mapping field names to error messages
 */
export function getFieldErrors(error: z.ZodError): Record<string, string> {
  const fieldErrors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    const fieldName = err.path.join('.');
    if (!fieldErrors[fieldName]) {
      fieldErrors[fieldName] = err.message;
    }
  });
  
  return fieldErrors;
}
