import React from 'react';
import { useAuth } from '@clerk/clerk-react';
import { Navigate, useLocation } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { api } from '@db';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

/**
 * ProtectedRoute component that handles authentication and authorization.
 * Shows loading state when user data is being fetched or created.
 * @param {ProtectedRouteProps} props - Component props
 * @param {React.ReactNode} props.children - Child components to render when authorized
 * @param {string[]} [props.allowedRoles] - Optional array of roles allowed to access this route
 * @returns {JSX.Element | null} The protected content or redirect/loading state
 */
export default function ProtectedRoute({
  children,
  allowedRoles,
}: ProtectedRouteProps): JSX.Element | null {
  const { userId, isLoaded } = useAuth();
  const user = useQuery(api.functions.users.getCurrentUser);
  const location = useLocation();

  const userRole = user?.role ?? 'member';

  if (!isLoaded) {
    return null;
  }

  if (!userId) {
    return <Navigate to="/sign-in" state={{ from: location }} replace />;
  }

  if (user === undefined) {
    return null;
  }

  if (user === null) {
    return <>{children}</>;
  }

  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
