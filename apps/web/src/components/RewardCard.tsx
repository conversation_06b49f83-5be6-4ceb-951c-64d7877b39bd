import React from 'react';
import { Lock } from 'lucide-react';
import { Doc } from '@db/types';

interface RewardCardProps {
  reward: Doc<'clientConfiguration'>['rewards'][number];
  userPoints: number;
  onRedeem: (rewardId: string) => void;
}

const RewardCard: React.FC<RewardCardProps> = ({
  reward,
  userPoints,
  onRedeem,
}) => {
  const canAfford = userPoints >= reward.cost;

  return (
    <div
      className={`relative rounded-lg border bg-white shadow-sm transition-all duration-300 ${
        !canAfford ? 'bg-gray-50 text-gray-400' : 'hover:shadow-lg'
      }`}
    >
      {!canAfford && (
        <div className="absolute inset-0 z-10 flex items-center justify-center rounded-lg bg-white bg-opacity-50">
          <Lock className="h-8 w-8 text-gray-400" />
        </div>
      )}
      <div className={`p-6 ${!canAfford ? 'opacity-60' : ''}`}>
        <h3 className="text-xl font-bold">{reward.name}</h3>
        <p
          className={`mt-2 text-2xl font-semibold ${
            canAfford ? 'text-indigo-600' : 'text-gray-400'
          }`}
        >
          {reward.cost.toLocaleString()} Points
        </p>
        {reward.description && (
          <p className="mt-2 text-sm text-gray-500">{reward.description}</p>
        )}
        <button
          onClick={() => onRedeem(reward.id)}
          disabled={!canAfford}
          className={`mt-4 w-full rounded-md px-4 py-2 font-semibold text-white transition-colors ${
            canAfford
              ? 'bg-indigo-600 hover:bg-indigo-700'
              : 'cursor-not-allowed bg-gray-300'
          }`}
        >
          {canAfford ? 'Redeem' : 'Not enough points'}
        </button>
      </div>
    </div>
  );
};

export default RewardCard;
