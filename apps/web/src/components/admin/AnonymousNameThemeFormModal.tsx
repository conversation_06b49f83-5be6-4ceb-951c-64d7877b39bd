import React, { useState, useEffect, Fragment } from 'react';
import { useMutation } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc } from '@db/types';
import { Dialog, Transition } from '@headlessui/react';

interface AnonymousNameThemeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  theme?: Doc<'anonymousNameTemplates'>;
}

export const AnonymousNameThemeFormModal: React.FC<
  AnonymousNameThemeFormModalProps
> = ({ isOpen, onClose, theme }) => {
  const [themeName, setThemeName] = useState('');
  const [adjectives, setAdjectives] = useState('');
  const [nouns, setNouns] = useState('');

  const createTheme = useMutation(api.functions.anonymousNameThemes.create);
  const updateTheme = useMutation(api.functions.anonymousNameThemes.update);
  const isEditing = theme !== undefined;

  useEffect(() => {
    if (theme && isOpen) {
      setThemeName(theme.themeName);
      setAdjectives(theme.adjectives.join(', '));
      setNouns(theme.nouns.join(', '));
    } else {
      setThemeName('');
      setAdjectives('');
      setNouns('');
    }
  }, [theme, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!themeName || !adjectives || !nouns) {
      toast.error('All fields are required.');
      return;
    }

    const mutationPromise = isEditing
      ? updateTheme({ id: theme!._id, themeName, adjectives, nouns })
      : createTheme({ themeName, adjectives, nouns });

    await toast.promise(mutationPromise, {
      loading: `${isEditing ? 'Updating' : 'Creating'} theme...`,
      success: `Theme successfully ${isEditing ? 'updated' : 'created'}!`,
      error: (err) =>
        err.data?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} theme.`,
    });

    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  {isEditing ? 'Edit Theme' : 'Create New Theme'}
                </Dialog.Title>
                <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                  <div>
                    <label
                      htmlFor="themeName"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Theme Name
                    </label>
                    <input
                      type="text"
                      id="themeName"
                      value={themeName}
                      onChange={(e) => setThemeName(e.target.value)}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="adjectives"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Adjectives (comma-separated)
                    </label>
                    <textarea
                      id="adjectives"
                      value={adjectives}
                      onChange={(e) => setAdjectives(e.target.value)}
                      required
                      rows={4}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="nouns"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Nouns (comma-separated)
                    </label>
                    <textarea
                      id="nouns"
                      value={nouns}
                      onChange={(e) => setNouns(e.target.value)}
                      required
                      rows={4}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
                    >
                      {isEditing ? 'Update Theme' : 'Create Theme'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
