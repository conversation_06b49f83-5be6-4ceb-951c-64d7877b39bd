import React, { useState, useEffect, useCallback } from 'react';
import { HugeIcon } from '../icons/HugeIcon';

interface IconOption {
  name: string;
  tags: string;
  category: string;
  featured: boolean;
  version: string;
}

interface IconSearchDropdownProps {
  value: string;
  onChange: (iconName: string) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Smart icon search dropdown component that provides intelligent icon suggestions
 * using the huge-icons library. Searches icons based on activity types, fitness terms,
 * and general descriptive words.
 *
 * @param value - Current selected icon name
 * @param onChange - Callback when icon selection changes
 * @param placeholder - Input placeholder text
 * @param className - Additional CSS classes
 */
const IconSearchDropdown: React.FC<IconSearchDropdownProps> = ({
  value,
  onChange,
  placeholder = 'Search for an icon...',
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<IconOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Debounced search function
  const searchIcons = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      // This would ideally call the MCP server, but for now we'll simulate with common fitness icons
      // In a real implementation, this would use the huge-icons search API
      const fitnessIconDatabase: IconOption[] = [
        // Gym Equipment
        {
          name: 'dumbbell-01',
          tags: 'fitness, strength, weight, gym, equipment',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'dumbbell-02',
          tags: 'fitness, strength, weight, gym, equipment',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'dumbbell-03',
          tags: 'fitness, strength, weight, gym, equipment',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'kettlebell',
          tags: 'kettlebell, weight, strength, fitness, equipment',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'yoga-mat',
          tags: 'yoga, mat, equipment, exercise, floor',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'treadmill-01',
          tags: 'cardio, running, exercise, fitness, machine',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'treadmill-02',
          tags: 'cardio, running, exercise, fitness, machine',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'boxing-glove-01',
          tags: 'boxing, glove, combat, fitness, training',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'skipping-rope',
          tags: 'rope, skipping, cardio, fitness, jump',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'equipment-gym-01',
          tags: 'equipment, gym, machine, fitness, strength',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        // Workouts & Activities
        {
          name: 'yoga-01',
          tags: 'yoga, meditation, flexibility, wellness, class',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'yoga-02',
          tags: 'yoga, meditation, flexibility, wellness, class',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'yoga-03',
          tags: 'yoga, meditation, flexibility, wellness, class',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'workout-run',
          tags: 'running, cardio, exercise, fitness, training',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'workout-squats',
          tags: 'squats, legs, exercise, fitness, strength',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'workout-stretching',
          tags: 'stretching, flexibility, exercise, wellness, recovery',
          category: 'gym',
          featured: false,
          version: '1.0.0',
        },
        // Check & Success
        {
          name: 'checkmark-circle-02',
          tags: 'check, complete, done, success, achievement',
          category: 'check',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'checkmark-badge-01',
          tags: 'check, badge, achievement, success, milestone',
          category: 'check',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'tick-01',
          tags: 'tick, check, complete, done, success',
          category: 'check',
          featured: false,
          version: '1.0.0',
        },
        // Calendar & Time
        {
          name: 'calendar-03',
          tags: 'schedule, time, appointment, date, class',
          category: 'date-time',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'calendar-check-in-01',
          tags: 'calendar, check-in, appointment, schedule, attendance',
          category: 'date-time',
          featured: false,
          version: '1.0.0',
        },
        {
          name: 'appointment-01',
          tags: 'appointment, booking, schedule, session, personal training',
          category: 'date-time',
          featured: false,
          version: '1.0.0',
        },
        // Activity & Business
        {
          name: 'activity-01',
          tags: 'activity, action, movement, fitness, tracking',
          category: 'business',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'activity-02',
          tags: 'activity, action, movement, fitness, tracking',
          category: 'business',
          featured: false,
          version: '1.0.0',
        },
        // Awards & Achievements
        {
          name: 'trophy-01',
          tags: 'trophy, award, achievement, winner, milestone',
          category: 'award',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'medal-first-place',
          tags: 'medal, first place, achievement, gold, winner',
          category: 'award',
          featured: true,
          version: '1.0.0',
        },
        // Personal Training & Classes
        {
          name: 'user-check-01',
          tags: 'personal, training, coach, instructor, member',
          category: 'users',
          featured: false,
          version: '1.0.0',
        },
        // Workshop & Education
        {
          name: 'check-list',
          tags: 'workshop, education, learning, training, class, checklist',
          category: 'education',
          featured: false,
          version: '1.0.0',
        },
      ].filter(
        (icon) =>
          icon.name.toLowerCase().includes(query.toLowerCase()) ||
          icon.tags.toLowerCase().includes(query.toLowerCase())
      );

      setSuggestions(fitnessIconDatabase.slice(0, 10));
    } catch (error) {
      console.error('Error searching icons:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm) {
        searchIcons(searchTerm);
      } else {
        setSuggestions([]);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, searchIcons]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    onChange(newValue);
    setIsOpen(true);
  };

  const handleSelectIcon = (iconName: string) => {
    setSearchTerm(iconName);
    onChange(iconName);
    setIsOpen(false);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (!searchTerm) {
      // Show some default popular icons when focused with no input
      setSuggestions([
        {
          name: 'dumbbell-01',
          tags: 'fitness, strength, weight, gym, equipment',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'yoga-01',
          tags: 'yoga, meditation, flexibility, wellness, class',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'workout-run',
          tags: 'running, cardio, exercise, fitness, training',
          category: 'gym',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'calendar-03',
          tags: 'schedule, time, appointment, date, class',
          category: 'date-time',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'checkmark-circle-02',
          tags: 'check, complete, done, success, achievement',
          category: 'check',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'trophy-01',
          tags: 'trophy, award, achievement, winner, milestone',
          category: 'award',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'activity-01',
          tags: 'activity, action, movement, fitness, tracking',
          category: 'business',
          featured: true,
          version: '1.0.0',
        },
        {
          name: 'appointment-01',
          tags: 'appointment, booking, schedule, session, personal training',
          category: 'date-time',
          featured: false,
          version: '1.0.0',
        },
      ]);
    }
  };

  const handleInputBlur = () => {
    // Delay closing to allow for click on suggestions
    setTimeout(() => setIsOpen(false), 150);
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2">
        <div className="relative flex-grow">
          <input
            type="text"
            value={searchTerm || value}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder={placeholder}
            className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${className}`}
          />

          {/* Dropdown */}
          {isOpen && (
            <div className="absolute z-50 mt-1 max-h-60 w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg">
              {isLoading && (
                <div className="px-3 py-2 text-sm text-gray-500">
                  Searching icons...
                </div>
              )}

              {!isLoading && suggestions.length === 0 && searchTerm && (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No icons found for "{searchTerm}"
                </div>
              )}

              {!isLoading &&
                suggestions.map((icon) => (
                  <button
                    key={icon.name}
                    type="button"
                    onClick={() => handleSelectIcon(icon.name)}
                    className="flex w-full items-center gap-3 border-b border-gray-100 px-3 py-2 text-left last:border-b-0 hover:bg-gray-50"
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded border border-gray-300 bg-gray-50">
                      <HugeIcon icon={icon.name} className="h-5 w-5" />
                    </div>
                    <div className="flex-grow">
                      <div className="text-sm font-medium text-gray-900">
                        {icon.name}
                      </div>
                      <div className="truncate text-xs text-gray-500">
                        {icon.category} •{' '}
                        {icon.tags.split(',').slice(0, 3).join(', ')}
                      </div>
                    </div>
                    {icon.featured && (
                      <span className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                        Featured
                      </span>
                    )}
                  </button>
                ))}
            </div>
          )}
        </div>

        {/* Preview */}
        <div className="flex h-10 w-10 items-center justify-center rounded-md border border-gray-300 bg-gray-50">
          <HugeIcon icon={value || searchTerm} className="h-6 w-6" />
        </div>
      </div>
    </div>
  );
};

export default IconSearchDropdown;
