import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc } from '@db/types';
import { HugeIcon } from '../icons/HugeIcon';
import IconSearchDropdown from './IconSearchDropdown';

interface MilestoneFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  milestone?: Doc<'milestones'>;
  activityTypes: Doc<'activityTypes'>[];
}

const MilestoneFormModal: React.FC<MilestoneFormModalProps> = ({
  isOpen,
  onClose,
  milestone,
  activityTypes,
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [activityTypeMatcher, setActivityTypeMatcher] = useState('');
  const [countThreshold, setCountThreshold] = useState(10);
  const [rewardPoints, setRewardPoints] = useState(100);
  const [iconUrl, setIconUrl] = useState('award-01');
  const [isEnabled, setIsEnabled] = useState(true);
  const [isRepeatable, setIsRepeatable] = useState(false);

  const createMilestone = useMutation(api.functions.milestones.createMilestone);
  const updateMilestone = useMutation(api.functions.milestones.updateMilestone);

  const isEditing = milestone !== undefined;

  useEffect(() => {
    if (isOpen) {
      if (isEditing) {
        setName(milestone.name);
        setDescription(milestone.description || '');
        setActivityTypeMatcher(milestone.conditions.activityTypeMatcher);
        setCountThreshold(milestone.conditions.countThreshold);
        setRewardPoints(
          (milestone.rewards.find((r) => r.type === 'points')
            ?.value as number) || 0
        );
        setIconUrl(milestone.iconUrl);
        setIsEnabled(milestone.isEnabled);
        setIsRepeatable(milestone.isRepeatable);
      } else {
        // Reset form for creation
        setName('');
        setDescription('');
        setActivityTypeMatcher(activityTypes[0]?.key || '');
        setCountThreshold(10);
        setRewardPoints(100);
        setIconUrl('award-01');
        setIsEnabled(true);
        setIsRepeatable(false);
      }
    }
  }, [milestone, isOpen, isEditing, activityTypes]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !iconUrl || countThreshold <= 0 || rewardPoints < 0) {
      toast.error(
        'Name, Icon URL, a positive count threshold, and non-negative reward points are required.'
      );
      return;
    }

    const mutationPromise = isEditing
      ? updateMilestone({
          milestoneId: milestone._id,
          name,
          description,
          iconUrl,
          triggerType: 'activity_count', // Currently locked
          activityTypeMatcher,
          countThreshold,
          rewardPoints,
          isEnabled,
          isRepeatable,
        })
      : createMilestone({
          name,
          description,
          iconUrl,
          triggerType: 'activity_count',
          activityTypeMatcher,
          countThreshold,
          rewardPoints,
          isEnabled,
          isRepeatable,
        });

    await toast.promise(mutationPromise, {
      loading: `${isEditing ? 'Updating' : 'Creating'} milestone...`,
      success: `Milestone successfully ${isEditing ? 'updated' : 'created'}!`,
      error: (err) =>
        err.data?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} milestone.`,
    });

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="w-full max-w-lg rounded-lg bg-white p-6 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-gray-800">
          {isEditing ? 'Edit Milestone' : 'Create New Milestone'}
        </h2>
        <form
          onSubmit={handleSubmit}
          className="mt-4 max-h-[70vh] space-y-4 overflow-y-auto pr-2"
        >
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700"
            >
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="activityType"
                className="block text-sm font-medium text-gray-700"
              >
                Activity Type
              </label>
              <select
                id="activityType"
                value={activityTypeMatcher}
                onChange={(e) => setActivityTypeMatcher(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                {activityTypes.map((at) => (
                  <option key={at.key} value={at.key}>
                    {at.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label
                htmlFor="countThreshold"
                className="block text-sm font-medium text-gray-700"
              >
                Count Threshold
              </label>
              <input
                type="number"
                id="countThreshold"
                value={countThreshold}
                onChange={(e) => setCountThreshold(Number(e.target.value))}
                required
                min="1"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="rewardPoints"
                className="block text-sm font-medium text-gray-700"
              >
                Point Reward
              </label>
              <input
                type="number"
                id="rewardPoints"
                value={rewardPoints}
                onChange={(e) => setRewardPoints(Number(e.target.value))}
                required
                min="0"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label
                htmlFor="iconUrl"
                className="block text-sm font-medium text-gray-700"
              >
                Icon Name
              </label>
              <IconSearchDropdown
                value={iconUrl}
                onChange={setIconUrl}
                placeholder="Search for an icon (e.g., trophy, medal, award)"
                className=""
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="isEnabled"
                type="checkbox"
                checked={isEnabled}
                onChange={(e) => setIsEnabled(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label
                htmlFor="isEnabled"
                className="ml-2 block text-sm text-gray-900"
              >
                Milestone is Enabled
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="isRepeatable"
                type="checkbox"
                checked={isRepeatable}
                onChange={(e) => setIsRepeatable(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label
                htmlFor="isRepeatable"
                className="ml-2 block text-sm text-gray-900"
              >
                Milestone is Repeatable
              </label>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
            >
              {isEditing ? 'Update Milestone' : 'Create Milestone'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MilestoneFormModal;
