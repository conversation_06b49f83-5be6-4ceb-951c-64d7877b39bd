/**
 * Shared layout component for admin pages.
 * Provides consistent navigation and structure for admin functionality.
 */

import { ReactNode, useState } from 'react';
import { 
  Users, 
  Gift, 
  Trophy, 
  CheckCircle, 
  Settings,
  Menu,
  X 
} from 'lucide-react';

interface AdminLayoutProps {
  children: ReactNode;
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

interface TabConfig {
  id: string;
  label: string;
  icon: ReactNode;
  description: string;
}

const adminTabs: TabConfig[] = [
  {
    id: 'rewards',
    label: 'Rewards',
    icon: <Gift className="h-5 w-5" />,
    description: 'Manage reward catalog and pricing',
  },
  {
    id: 'milestones',
    label: 'Milestones',
    icon: <Trophy className="h-5 w-5" />,
    description: 'Configure achievement milestones',
  },
  {
    id: 'fulfillment',
    label: 'Fulfillment',
    icon: <CheckCircle className="h-5 w-5" />,
    description: 'Process reward redemptions',
  },
  {
    id: 'users',
    label: 'Users',
    icon: <Users className="h-5 w-5" />,
    description: 'Manage user accounts and roles',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings className="h-5 w-5" />,
    description: 'Configure system settings',
  },
];

/**
 * AdminLayout provides a consistent structure for admin pages.
 * @param {AdminLayoutProps} props - Component props
 * @param {ReactNode} props.children - Content to render in the main area
 * @param {string} [props.currentTab] - Currently active tab
 * @param {Function} [props.onTabChange] - Callback when tab is changed
 * @returns {JSX.Element} The admin layout component
 */
export default function AdminLayout({
  children,
  currentTab,
  onTabChange,
}: AdminLayoutProps): JSX.Element {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  /**
   * Handles tab selection.
   * @param {string} tabId - The ID of the selected tab
   */
  const handleTabSelect = (tabId: string): void => {
    if (onTabChange) {
      onTabChange(tabId);
    }
    setIsMobileMenuOpen(false);
  };

  /**
   * Toggles the mobile menu.
   */
  const toggleMobileMenu = (): void => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900">Admin Panel</h1>
          <button
            onClick={toggleMobileMenu}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {adminTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabSelect(tab.id)}
                className={`
                  w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  ${currentTab === tab.id
                    ? 'bg-indigo-100 text-indigo-700 border-r-2 border-indigo-500'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <span className="mr-3">{tab.icon}</span>
                <div className="text-left">
                  <div className="font-medium">{tab.label}</div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {tab.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            Admin Dashboard v1.0
          </div>
        </div>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={toggleMobileMenu}
        />
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Mobile header */}
        <div className="lg:hidden flex items-center justify-between h-16 px-4 bg-white border-b border-gray-200">
          <button
            onClick={toggleMobileMenu}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Admin Panel</h1>
          <div className="w-10" /> {/* Spacer for centering */}
        </div>

        {/* Content area */}
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

/**
 * Hook to get admin tab configuration.
 * @returns {TabConfig[]} Array of admin tab configurations
 */
export function useAdminTabs(): TabConfig[] {
  return adminTabs;
}

/**
 * Utility function to get tab configuration by ID.
 * @param {string} tabId - The tab ID to find
 * @returns {TabConfig | undefined} The tab configuration or undefined
 */
export function getTabById(tabId: string): TabConfig | undefined {
  return adminTabs.find(tab => tab.id === tabId);
}
