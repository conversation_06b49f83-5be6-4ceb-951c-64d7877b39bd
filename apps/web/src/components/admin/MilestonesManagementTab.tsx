import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc, Id } from '@db/types';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import MilestoneFormModal from './MilestoneFormModal';
import { WithLoading } from '../utils/WithLoading';

const MilestonesManagementTab = () => {
  const milestones = useQuery(api.functions.milestones.getMilestonesForClient);
  const deleteMilestone = useMutation(api.functions.milestones.deleteMilestone);
  const activityTypes = useQuery(
    api.functions.milestones.getActivityTypesForClient
  );

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<
    Doc<'milestones'> | undefined
  >(undefined);

  const handleOpenCreateModal = () => {
    setSelectedMilestone(undefined);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (milestone: Doc<'milestones'>) => {
    setSelectedMilestone(milestone);
    setIsModalOpen(true);
  };

  const handleDelete = (milestoneId: Id<'milestones'>) => {
    if (
      window.confirm(
        'Are you sure you want to delete this milestone? This may be a soft delete if users have progress.'
      )
    ) {
      toast.promise(deleteMilestone({ milestoneId }), {
        loading: 'Deleting milestone...',
        success: (res) =>
          `Milestone ${res.wasSoftDeleted ? 'disabled' : 'deleted'}!`,
        error: 'Failed to delete milestone.',
      });
    }
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Manage Milestones</h2>
        <button
          onClick={handleOpenCreateModal}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          disabled={!activityTypes}
        >
          <PlusCircle className="mr-2 h-5 w-5" />
          Create Milestone
        </button>
      </div>

      <WithLoading
        isPending={milestones === undefined || activityTypes === undefined}
      >
        <div className="overflow-x-auto rounded-lg border">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Trigger
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Reward
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Status
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {milestones && milestones.length === 0 ? (
                <tr>
                  <td
                    colSpan={5}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No milestones created yet.
                  </td>
                </tr>
              ) : (
                milestones &&
                milestones.map((milestone) => (
                  <tr key={milestone._id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {milestone.name}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {`Complete ${milestone.conditions.countThreshold} ${
                        activityTypes?.find(
                          (at) =>
                            at.key === milestone.conditions.activityTypeMatcher
                        )?.name || milestone.conditions.activityTypeMatcher
                      }`}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {`${(
                        milestone.rewards.find((r) => r.type === 'points')
                          ?.value || 0
                      ).toLocaleString()} Points`}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          milestone.isEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {milestone.isEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="space-x-2 whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenEditModal(milestone)}
                        className="text-indigo-600 hover:text-indigo-900"
                        disabled={!activityTypes}
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(milestone._id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </WithLoading>

      {activityTypes && (
        <MilestoneFormModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          milestone={selectedMilestone}
          activityTypes={activityTypes}
        />
      )}
    </div>
  );
};

export default MilestonesManagementTab;
