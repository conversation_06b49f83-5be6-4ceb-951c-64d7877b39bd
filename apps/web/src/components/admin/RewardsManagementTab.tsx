import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc, Id } from '@db/_generated/dataModel';
import RewardFormModal from './RewardFormModal';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { WithLoading } from '../utils/WithLoading';
import { ConfirmationDialog } from '../ui/ConfirmationDialog';

const RewardsManagementTab = () => {
  const rewards = useQuery(api.functions.rewards.list);
  const deleteReward = useMutation(api.functions.rewards.del);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rewardToDelete, setRewardToDelete] = useState<Doc<'rewards'> | null>(
    null
  );
  const [selectedReward, setSelectedReward] = useState<
    Doc<'rewards'> | undefined
  >(undefined);

  const handleOpenCreateModal = () => {
    setSelectedReward(undefined);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (reward: Doc<'rewards'>) => {
    setSelectedReward(reward);
    setIsModalOpen(true);
  };

  const handleDeleteClick = (reward: Doc<'rewards'>) => {
    setRewardToDelete(reward);
  };

  const executeDelete = () => {
    if (!rewardToDelete) return;
    toast.promise(deleteReward({ rewardId: rewardToDelete._id }), {
      loading: 'Deleting reward...',
      success: 'Reward deleted!',
      error: 'Failed to delete reward.',
    });
    setRewardToDelete(null);
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Manage Rewards</h2>
        <button
          onClick={handleOpenCreateModal}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          <PlusCircle className="mr-2 h-5 w-5" />
          Create Reward
        </button>
      </div>

      <WithLoading isPending={rewards === undefined}>
        <div className="overflow-x-auto rounded-lg border">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Cost
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Status
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {rewards && rewards.length === 0 ? (
                <tr>
                  <td
                    colSpan={4}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No rewards created yet.
                  </td>
                </tr>
              ) : (
                rewards?.map((reward) => (
                  <tr key={reward._id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {reward.name}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {reward.cost}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          reward.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {reward.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="space-x-2 whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenEditModal(reward)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(reward)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </WithLoading>

      <RewardFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        reward={selectedReward}
      />

      {rewardToDelete && (
        <ConfirmationDialog
          isOpen={!!rewardToDelete}
          onClose={() => setRewardToDelete(null)}
          onConfirm={executeDelete}
          title="Delete Reward"
          description={`Are you sure you want to delete the reward "${rewardToDelete.name}"? This action cannot be undone.`}
          confirmButtonText="Yes, Delete"
          variant="danger"
        />
      )}
    </div>
  );
};

export default RewardsManagementTab;
