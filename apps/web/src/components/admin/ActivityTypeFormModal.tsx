import React, { useState, useEffect, useCallback } from 'react';
import { useMutation } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@fitness-rewards/db';
import { Doc } from '@fitness-rewards/db/convex/_generated/dataModel';
import {
  activityTypeCreateSchema,
  activityTypeUpdateSchema,
  ActivityTypeCreate,
  ActivityTypeUpdate,
} from '@fitness-rewards/shared/schemas/activityTypeSchemas';
import { z } from 'zod';
import { HugeIcon } from '../icons/HugeIcon';
import IconSearchDropdown from './IconSearchDropdown';

interface ActivityTypeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  activity?: Doc<'activityTypes'>;
}

/**
 * Converts a string to kebab-case by replacing spaces with hyphens
 * and converting to lowercase. Removes special characters except hyphens.
 *
 * @param str - The input string to convert
 * @returns The kebab-case formatted string
 */
function toKebabCase(str: string) {
  return str
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '') // Remove special characters except hyphens
    .replace(/-+/g, '-') // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading and trailing hyphens
}

const ActivityTypeFormModal: React.FC<ActivityTypeFormModalProps> = ({
  isOpen,
  onClose,
  activity,
}) => {
  const [name, setName] = useState('');
  const [key, setKey] = useState('');
  const [points, setPoints] = useState(10);
  const [iconName, setIconName] = useState('check-list');
  const [errors, setErrors] = useState<z.ZodError | null>(null);

  const createActivityType = useMutation(api.functions.activityTypes.create);
  const updateActivityType = useMutation(api.functions.activityTypes.update);

  const isEditing = activity !== undefined;

  useEffect(() => {
    if (isOpen) {
      if (isEditing) {
        setName(activity.name);
        setKey(activity.key);
        setPoints(activity.points);
        setIconName(activity.iconUrl);
      } else {
        // Reset form for creation
        setName('');
        setKey('');
        setPoints(10);
        setIconName('check-list');
      }
      setErrors(null);
    }
  }, [activity, isOpen, isEditing]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    if (!isEditing || key === toKebabCase(name)) {
      // only auto-update key if it was matching the name
      setKey(toKebabCase(newName));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const data = {
      name,
      key,
      points,
      iconName,
      ...(isEditing && { id: activity._id }),
    };

    const schema = isEditing
      ? activityTypeUpdateSchema
      : activityTypeCreateSchema;
    const validationResult = schema.safeParse(data);

    if (!validationResult.success) {
      setErrors(validationResult.error);
      toast.error('Please fix the errors in the form.');
      return;
    }
    setErrors(null);

    const mutationPromise = isEditing
      ? updateActivityType(data as ActivityTypeUpdate)
      : createActivityType(data as ActivityTypeCreate);

    await toast.promise(mutationPromise, {
      loading: `${isEditing ? 'Updating' : 'Creating'} activity type...`,
      success: `Activity type successfully ${isEditing ? 'updated' : 'created'}!`,
      error: (err) =>
        err.data?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} activity type.`,
    });

    onClose();
  };

  if (!isOpen) return null;

  const getError = (path: string) =>
    errors?.errors.find((e) => e.path[0] === path)?.message;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-gray-800">
          {isEditing ? 'Edit Activity Type' : 'Create New Activity Type'}
        </h2>
        <form onSubmit={handleSubmit} className="mt-4 space-y-4">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={handleNameChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
            {getError('name') && (
              <p className="mt-1 text-xs text-red-500">{getError('name')}</p>
            )}
          </div>
          <div>
            <label
              htmlFor="key"
              className="block text-sm font-medium text-gray-700"
            >
              Key
            </label>
            <input
              type="text"
              id="key"
              value={key}
              onChange={(e) => setKey(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm sm:text-sm"
            />
            {getError('key') && (
              <p className="mt-1 text-xs text-red-500">{getError('key')}</p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="points"
                className="block text-sm font-medium text-gray-700"
              >
                Points
              </label>
              <input
                type="number"
                id="points"
                value={points}
                onChange={(e) => setPoints(Number(e.target.value))}
                min="1"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              {getError('points') && (
                <p className="mt-1 text-xs text-red-500">
                  {getError('points')}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="iconName"
                className="block text-sm font-medium text-gray-700"
              >
                Icon Name
              </label>
              <IconSearchDropdown
                value={iconName}
                onChange={setIconName}
                placeholder="Search for an icon (e.g., dumbbell, yoga, calendar)"
                className="mt-1"
              />
            </div>
            {getError('iconName') && (
              <p className="mt-1 text-xs text-red-500">
                {getError('iconName')}
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
            >
              {isEditing ? 'Save Changes' : 'Create Activity'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ActivityTypeFormModal;
