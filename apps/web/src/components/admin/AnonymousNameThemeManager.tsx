import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@db';
import { WithLoading } from '../utils/WithLoading';
import { Doc } from '@db/types';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { AnonymousNameThemeFormModal } from './AnonymousNameThemeFormModal';
import { ConfirmationDialog } from '../ui/ConfirmationDialog';
import toast from 'react-hot-toast';

export const AnonymousNameThemeManager = () => {
  const themes = useQuery(api.functions.anonymousNameThemes.list);
  const deleteTheme = useMutation(api.functions.anonymousNameThemes.del);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<
    Doc<'anonymousNameTemplates'> | undefined
  >(undefined);
  const [themeToDelete, setThemeToDelete] =
    useState<Doc<'anonymousNameTemplates'> | null>(null);

  const handleOpenCreateModal = () => {
    setSelectedTheme(undefined);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (theme: Doc<'anonymousNameTemplates'>) => {
    setSelectedTheme(theme);
    setIsModalOpen(true);
  };

  const handleDeleteClick = (theme: Doc<'anonymousNameTemplates'>) => {
    setThemeToDelete(theme);
  };

  const executeDelete = () => {
    if (!themeToDelete) return;
    toast.promise(deleteTheme({ id: themeToDelete._id }), {
      loading: 'Deleting theme...',
      success: 'Theme deleted!',
      error: 'Failed to delete theme.',
    });
    setThemeToDelete(null);
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Custom Themes</h3>
        <button
          onClick={handleOpenCreateModal}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          <PlusCircle className="mr-2 h-5 w-5" />
          Create Theme
        </button>
      </div>

      <WithLoading isPending={themes === undefined}>
        <div className="overflow-x-auto rounded-lg border">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Theme Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Adjectives
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Nouns
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {themes && themes.length === 0 ? (
                <tr>
                  <td
                    colSpan={4}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No themes created yet.
                  </td>
                </tr>
              ) : (
                themes?.map((theme) => (
                  <tr key={theme._id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {theme.themeName}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {theme.adjectives.length} words
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {theme.nouns.length} words
                    </td>
                    <td className="space-x-2 whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenEditModal(theme)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(theme)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </WithLoading>

      <AnonymousNameThemeFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        theme={selectedTheme}
      />

      {themeToDelete && (
        <ConfirmationDialog
          isOpen={!!themeToDelete}
          onClose={() => setThemeToDelete(null)}
          onConfirm={executeDelete}
          title="Delete Theme"
          description={`Are you sure you want to delete the theme "${themeToDelete.themeName}"? This action cannot be undone.`}
          confirmButtonText="Yes, Delete"
          variant="danger"
        />
      )}
    </div>
  );
};
