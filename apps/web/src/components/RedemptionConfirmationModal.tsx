import React from 'react';
import { Doc } from '@db/types';

interface RedemptionConfirmationModalProps {
  isOpen: boolean;
  reward: Doc<'clientConfiguration'>['rewards'][number] | null;
  userPoints: number;
  isRedeeming: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const RedemptionConfirmationModal: React.FC<
  RedemptionConfirmationModalProps
> = ({ isOpen, reward, userPoints, isRedeeming, onConfirm, onCancel }) => {
  if (!reward || !isOpen) return null;

  const newBalance = userPoints - reward.cost;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onCancel}
    >
      <div
        className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-gray-800">Confirm Redemption</h2>
        <p className="mt-4 text-gray-600">
          You are about to redeem{' '}
          <span className="font-bold text-indigo-600">{reward.name}</span> for{' '}
          <span className="font-bold">{reward.cost.toLocaleString()}</span>{' '}
          points.
        </p>
        <div className="mt-4 rounded-md bg-gray-50 p-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Current Balance:</span>
            <span className="font-medium text-gray-700">
              {userPoints.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Points Spent:</span>
            <span className="font-medium text-red-500">
              - {reward.cost.toLocaleString()}
            </span>
          </div>
          <hr className="my-2" />
          <div className="flex justify-between font-bold">
            <span className="text-gray-600">New Balance:</span>
            <span className="text-indigo-600">
              {newBalance.toLocaleString()}
            </span>
          </div>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onCancel}
            disabled={isRedeeming}
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={isRedeeming}
            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isRedeeming ? 'Redeeming...' : 'Confirm'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RedemptionConfirmationModal;
