import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@db';
import { TIER_THRESHOLDS } from '@fitness-rewards/core/services/TierService';
import toast from 'react-hot-toast';
import { Doc } from '@db/types';

interface TierSelectorProps {
  user: Doc<'users'>;
}

export default function TierSelector({ user }: TierSelectorProps) {
  const setTier = useMutation(api.dev.setTierForUser);
  const resetTier = useMutation(api.dev.resetTierForUser);
  const [isLoading, setIsLoading] = useState(false);

  const handleTierChange = async (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const selectedTier = event.target.value;
    setIsLoading(true);
    const toastId = toast.loading(`Setting tier to ${selectedTier}...`);

    try {
      if (selectedTier === 'Reset') {
        await resetTier();
      } else {
        await setTier({ tier: selectedTier });
      }
      toast.success(`Tier successfully set to ${selectedTier}!`, {
        id: toastId,
      });
    } catch (error) {
      console.error('Failed to set tier', error);
      toast.error('Failed to set tier.', { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="rounded-lg border border-yellow-300 bg-yellow-100 p-4">
      <h3 className="text-lg font-bold text-yellow-800">🔧 Admin Controls</h3>
      <p className="mb-2 text-sm text-yellow-700">
        Use this to simulate different tier levels for the current user. The
        dashboard will update in real-time.
      </p>
      <select
        className="w-full rounded-md border border-gray-300 p-2"
        onChange={handleTierChange}
        disabled={isLoading}
        value={user.tier || ''}
      >
        <option value="Reset">Reset to Default (Bronze)</option>
        {TIER_THRESHOLDS.map((tier) => (
          <option key={tier.tier} value={tier.tier}>
            {tier.tier} ({tier.pointsRequired} pts)
          </option>
        ))}
      </select>
      {isLoading && <p className="mt-2 animate-pulse text-sm">Updating...</p>}
    </div>
  );
}
