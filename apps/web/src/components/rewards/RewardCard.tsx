import React from 'react';
import { Doc } from '@db/_generated/dataModel';
import { Lock } from 'lucide-react';
import { HugeIcon } from '../icons/HugeIcon';

interface RewardCardProps {
  reward: Doc<'rewards'>;
  userPoints: number;
  onRedeemClick: (reward: Doc<'rewards'>) => void;
}

export const RewardCard: React.FC<RewardCardProps> = ({
  reward,
  userPoints,
  onRedeemClick,
}) => {
  const canAfford = userPoints >= reward.cost;

  return (
    <div
      className={`group relative flex flex-col rounded-lg border bg-white shadow-sm transition-all ${
        canAfford
          ? 'hover:-translate-y-1 hover:shadow-lg'
          : 'opacity-60 saturate-50'
      }`}
    >
      <div className="relative flex h-48 w-full items-center justify-center overflow-hidden rounded-t-lg bg-gray-100">
        <HugeIcon
          icon={reward.imageUrl || 'gift'}
          className="h-20 w-20 text-gray-400"
        />
        {!canAfford && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50">
            <Lock className="h-10 w-10 text-white" />
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col p-4">
        <h3 className="text-lg font-semibold text-gray-800">{reward.name}</h3>
        {reward.description && (
          <p className="mt-1 flex-1 text-sm text-gray-600">
            {reward.description}
          </p>
        )}
        <div className="mt-4">
          <button
            onClick={() => onRedeemClick(reward)}
            disabled={!canAfford}
            className="w-full rounded-md px-4 py-2 text-sm font-semibold text-white transition-colors enabled:bg-indigo-600 enabled:hover:bg-indigo-700 disabled:cursor-not-allowed disabled:bg-gray-300"
          >
            {canAfford
              ? `Redeem for ${reward.cost.toLocaleString()} pts`
              : 'Not enough points'}
          </button>
        </div>
      </div>
    </div>
  );
};
