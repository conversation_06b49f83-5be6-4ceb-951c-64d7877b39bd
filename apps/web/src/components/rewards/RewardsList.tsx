import React, { useState } from 'react';
import { Doc } from '@db/_generated/dataModel';
import { RewardCard } from './RewardCard';
import { ConfirmationDialog } from '../ui/ConfirmationDialog';
import { useMutation } from 'convex/react';
import { api } from '@db';
import toast from 'react-hot-toast';

interface RewardsListProps {
  rewards: Doc<'rewards'>[];
  userPoints: number;
}

export const RewardsList: React.FC<RewardsListProps> = ({
  rewards,
  userPoints,
}) => {
  const [rewardToRedeem, setRewardToRedeem] = useState<Doc<'rewards'> | null>(
    null
  );
  const redeemMutation = useMutation(api.functions.rewards.redeem);

  const handleRedeemClick = (reward: Doc<'rewards'>) => {
    if (userPoints >= reward.cost) {
      setRewardToRedeem(reward);
    } else {
      toast.error("You don't have enough points for this reward.");
    }
  };

  const executeRedemption = () => {
    if (!rewardToRedeem) return;

    toast.promise(redeemMutation({ rewardId: rewardToRedeem._id }), {
      loading: 'Redeeming reward...',
      success: `Successfully redeemed "${rewardToRedeem.name}"!`,
      error: (err) => err.data || 'Failed to redeem reward.',
    });
    setRewardToRedeem(null);
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {rewards.map((reward) => (
          <RewardCard
            key={reward._id}
            reward={reward}
            userPoints={userPoints}
            onRedeemClick={handleRedeemClick}
          />
        ))}
      </div>
      {rewardToRedeem && (
        <ConfirmationDialog
          isOpen={!!rewardToRedeem}
          onClose={() => setRewardToRedeem(null)}
          onConfirm={executeRedemption}
          title="Confirm Redemption"
          description={`Are you sure you want to redeem "${
            rewardToRedeem.name
          }" for ${rewardToRedeem.cost.toLocaleString()} points?`}
          confirmButtonText="Yes, Redeem"
        />
      )}
    </>
  );
};
