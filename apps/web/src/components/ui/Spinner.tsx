import React from 'react';

interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  fullscreen?: boolean;
}

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  fullscreen = false,
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-4',
    lg: 'h-16 w-16 border-4',
  };

  const spinner = (
    <div
      className={`animate-spin rounded-full border-gray-300 border-t-indigo-600 ${sizeClasses[size]}`}
    />
  );

  if (fullscreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-75">
        {spinner}
      </div>
    );
  }

  return spinner;
};
