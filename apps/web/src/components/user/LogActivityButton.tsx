import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { HugeIcon } from '../icons/HugeIcon';
import toast from 'react-hot-toast';
import { WithLoading } from '../utils/WithLoading';

export const LogActivityButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [loggingActivityKey, setLoggingActivityKey] = useState<string | null>(
    null
  );
  const activityTypes = useQuery(api.functions.activityTypes.getForUser);
  const logActivity = useMutation(api.functions.activities.logActivity);

  const handleLogActivity = async (activityTypeKey: string) => {
    setLoggingActivityKey(activityTypeKey);

    const promise = logActivity({ activityTypeKey }).then((result) => {
      setIsOpen(false);
      return result;
    });

    toast.promise(promise, {
      loading: 'Logging activity...',
      success: (result) => `Activity logged! +${result.pointsAwarded} points`,
      error: (err) => err.data?.message || 'Failed to log activity.',
    });

    // Reset loading state regardless of outcome
    promise.finally(() => setLoggingActivityKey(null));
  };

  return (
    <div className="relative inline-block text-left">
      <div>
        <button
          type="button"
          className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
          id="menu-button"
          aria-expanded={isOpen}
          aria-haspopup="true"
          onClick={() => setIsOpen(!isOpen)}
        >
          Log Activity
          <HugeIcon
            icon="arrow-down-01"
            className="-mr-1 h-5 w-5 text-gray-400"
          />
        </button>
      </div>

      {isOpen && (
        <div
          className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="menu-button"
        >
          <div className="py-1" role="none">
            <WithLoading isPending={activityTypes === undefined}>
              {activityTypes && activityTypes.length > 0 ? (
                activityTypes.map((activity) => (
                  <button
                    key={activity.key}
                    onClick={() => handleLogActivity(activity.key)}
                    disabled={!!loggingActivityKey}
                    className="flex w-full items-center gap-3 px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
                    role="menuitem"
                  >
                    <HugeIcon icon={activity.iconUrl} className="h-5 w-5" />
                    <span>{activity.name}</span>
                    {loggingActivityKey === activity.key && (
                      <HugeIcon
                        icon="loading-02"
                        className="h-4 w-4 animate-spin"
                      />
                    )}
                  </button>
                ))
              ) : (
                <p className="px-4 py-2 text-sm text-gray-500">
                  No activities available.
                </p>
              )}
            </WithLoading>
          </div>
        </div>
      )}
    </div>
  );
};
