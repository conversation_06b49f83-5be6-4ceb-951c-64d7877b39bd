import { useMutation, useQuery } from 'convex/react';
import { api } from '@db';
import toast from 'react-hot-toast';
import { Shield, User, Eye, EyeOff } from 'lucide-react';
import { Switch } from '@headlessui/react';
import { Doc } from '@db/types';

function classNames(...classes: (string | boolean)[]) {
  return classes.filter(Boolean).join(' ');
}

interface LeaderboardSettingsProps {
  user: Doc<'users'>;
}

/**
 * A component allowing users to manage their leaderboard privacy settings.
 * Displays a toggle to switch between an anonymous and public identity.
 *
 * @param {LeaderboardSettingsProps} props The component props.
 * @returns {JSX.Element} The leaderboard settings card component.
 */
export function LeaderboardSettings({ user }: LeaderboardSettingsProps) {
  const updateSettings = useMutation(
    api.functions.users.updateLeaderboardSettings
  );

  const showRealName = user.leaderboardSettings?.showRealName ?? false;

  const handleToggle = async (newVal: boolean) => {
    const toastId = toast.loading('Updating privacy settings...');
    try {
      await updateSettings({ showRealName: newVal });
      toast.success('Settings updated!', { id: toastId });
    } catch (error) {
      toast.error('Failed to update settings.', { id: toastId });
      console.error(error);
    }
  };

  return (
    <div className="rounded-lg bg-white p-6 shadow-md">
      <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold">
        <Shield className="h-6 w-6 text-indigo-600" />
        Leaderboard Privacy
      </h2>
      <p className="mb-4 text-sm text-gray-600">
        Choose how you appear on the public leaderboard. You can be fully
        anonymous or show your real name.
      </p>

      <div className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
        <div className="flex items-center">
          <div
            className={`mr-3 flex h-8 w-8 items-center justify-center rounded-full ${
              showRealName ? 'bg-blue-100' : 'bg-gray-200'
            }`}
          >
            {showRealName ? (
              <User className="h-5 w-5 text-blue-600" />
            ) : (
              <User className="h-5 w-5 text-gray-600" />
            )}
          </div>
          <div>
            <p
              className={`font-medium ${
                showRealName ? 'text-blue-800' : 'text-gray-800'
              }`}
            >
              {showRealName ? 'Public Identity' : 'Anonymous Identity'}
            </p>
            <p className="text-xs text-gray-500">
              {showRealName
                ? 'Your real name is shown'
                : 'Your anonymous name is shown'}
            </p>
          </div>
        </div>
        <Switch
          checked={showRealName}
          onChange={handleToggle}
          className={classNames(
            showRealName ? 'bg-indigo-600' : 'bg-gray-200',
            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2'
          )}
        >
          <span className="sr-only">Use setting</span>
          <span
            className={classNames(
              showRealName ? 'translate-x-5' : 'translate-x-0',
              'pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
            )}
          >
            <span
              className={classNames(
                showRealName
                  ? 'opacity-0 duration-100 ease-out'
                  : 'opacity-100 duration-200 ease-in',
                'absolute inset-0 flex h-full w-full items-center justify-center transition-opacity'
              )}
              aria-hidden="true"
            >
              <EyeOff className="h-3 w-3 text-gray-400" />
            </span>
            <span
              className={classNames(
                showRealName
                  ? 'opacity-100 duration-200 ease-in'
                  : 'opacity-0 duration-100 ease-out',
                'absolute inset-0 flex h-full w-full items-center justify-center transition-opacity'
              )}
              aria-hidden="true"
            >
              <Eye className="h-3 w-3 text-indigo-600" />
            </span>
          </span>
        </Switch>
      </div>
      <div className="mt-4 text-center text-xs text-gray-500">
        Your anonymous name is:{' '}
        <span className="font-semibold text-gray-700">
          {user.leaderboardSettings?.anonymousName}
        </span>
      </div>
    </div>
  );
} 