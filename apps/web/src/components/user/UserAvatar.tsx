import Avatar from 'boring-avatars';
import { useQuery } from 'convex/react';
import { api } from '@db';
import { Doc } from '@db/types';

/**
 * Default color palette for avatars if client-specific colors are not available.
 * Picked to be visually appealing and distinct.
 */
const FALLBACK_AVATAR_COLORS = [
  '#2D2D2D',
  '#D4A574',
  '#A8B5A0',
  '#E8C547',
  '#E07A39',
];

interface UserAvatarProps {
  /** The user object containing identity and leaderboard settings */
  user: Partial<Doc<'users'>> & {
    leaderboardSettings?: {
      showRealName: boolean;
      anonymousName: string;
      avatarSeed: string;
    } | null;
    profileImageUrl?: string | null;
  };
  /** Additional Tailwind CSS classes */
  className?: string;
}

/**
 * Renders a user's avatar.
 * Conditionally displays either the user's real profile picture or an
 * algorithmically generated, anonymous avatar based on their privacy settings.
 * The anonymous avatar is styled with client-specific brand colors.
 *
 * @param {UserAvatarProps} props The component props.
 * @returns {JSX.Element} The rendered avatar component.
 */
export function UserAvatar({ user, className = 'h-10 w-10' }: UserAvatarProps) {
  const clientConfig = useQuery(
    api.functions.clientConfigs.getCurrentClientConfig
  );

  const showRealIdentity = user.leaderboardSettings?.showRealName === true;
  const avatarSeed = user.leaderboardSettings?.avatarSeed ?? user._id;
  const anonymousName =
    user.leaderboardSettings?.anonymousName ?? 'Anonymous User';

  const avatarColors =
    clientConfig?.branding?.avatarColors &&
    clientConfig.branding.avatarColors.length > 0
      ? clientConfig.branding.avatarColors
      : FALLBACK_AVATAR_COLORS;

  if (showRealIdentity && user.profileImageUrl) {
    return (
      <img
        src={user.profileImageUrl}
        alt={user.firstName ?? 'User'}
        className={`${className} rounded-full object-cover`}
        title={user.firstName ?? 'User'}
      />
    );
  }

  return (
    <div className={className} title={anonymousName}>
      <Avatar
        size="100%"
        name={avatarSeed}
        variant="bauhaus"
        colors={avatarColors}
      />
    </div>
  );
}
