import React from 'react';

/**
 * Universal loading spinner component that covers the entire screen.
 * Uses the application's color scheme for consistent branding.
 * @returns {JSX.Element} A full-screen loading overlay with spinner
 */
const UniversalLoader: React.FC = (): JSX.Element => {
  return (
    <div className="fixed inset-0 z-[100] flex h-screen w-screen items-center justify-center bg-[#F5F2E8]">
      <div className="h-16 w-16 animate-spin rounded-full border-4 border-[#E6D7C3] border-t-[#D4A574]" />
    </div>
  );
};

export default UniversalLoader;
