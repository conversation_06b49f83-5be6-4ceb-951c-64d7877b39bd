import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RouterProvider,
  Outlet,
  useRouteError,
  isRouteErrorResponse,
  Link,
} from 'react-router-dom';
import { SignedIn, SignedOut, UserButton } from '@clerk/clerk-react';
import { api } from '@db';
import { useQuery } from 'convex/react';

import IndexPage from './pages/IndexPage';
import SignInPage from './pages/SignInPage';
import SignUpPage from './pages/SignUpPage';
import DashboardPage from './pages/DashboardPage';
import RewardsPage from './pages/RewardsPage';
import AdminPage from './pages/AdminPage';
import ProtectedRoute from './components/ProtectedRoute';
import { ErrorBoundary, AsyncErrorBoundary } from './components/ErrorBoundary';
import LeaderboardPage from './pages/LeaderboardPage';
import HistoryLayout from './pages/history/HistoryLayout';
import ActivityHistoryPage from './pages/history/ActivityHistoryPage';
import MilestonesHistoryPage from './pages/history/MilestonesHistoryPage';

/**
 * Error boundary component for route-level error handling
 */
function RouteErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900">{error.status}</h1>
          <p className="mt-2 text-xl text-gray-600">{error.statusText}</p>
          <p className="mt-4 text-gray-500">
            {error.status === 404
              ? "The page you're looking for doesn't exist."
              : 'Something went wrong.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900">Error</h1>
        <p className="mt-2 text-xl text-gray-600">Something went wrong</p>
        <p className="mt-4 text-gray-500">
          {error instanceof Error
            ? error.message
            : 'An unexpected error occurred.'}
        </p>
      </div>
    </div>
  );
}

/**
 * Simple outlet wrapper for route rendering
 */
function SimpleOutlet() {
  return <Outlet />;
}

/**
 * Main layout component with navigation and footer
 */
function MainLayout() {
  const user = useQuery(api.functions.users.getCurrentUser);
  const userRole = user?.role ?? 'member';
  const isAdminOrStaff = userRole === 'admin' || userRole === 'staff';

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 bg-white shadow-md">
        <nav className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
          <Link
            to="/"
            className="text-2xl font-bold text-indigo-600 hover:text-indigo-700"
          >
            FitRewards
          </Link>
          <div className="flex items-center space-x-4">
            <SignedIn>
              <Link
                to="/dashboard"
                className="text-gray-600 transition-colors hover:text-indigo-600"
              >
                Dashboard
              </Link>
              <Link
                to="/rewards"
                className="text-gray-600 transition-colors hover:text-indigo-600"
              >
                Rewards
              </Link>
              <Link
                to="/leaderboard"
                className="text-gray-600 transition-colors hover:text-indigo-600"
              >
                Leaderboard
              </Link>
              <Link
                to="/history/activities"
                className="text-gray-600 transition-colors hover:text-indigo-600"
              >
                History
              </Link>
              {isAdminOrStaff && (
                <Link
                  to="/admin"
                  className="text-sm font-semibold text-indigo-600 transition-colors hover:text-indigo-700"
                >
                  Admin
                </Link>
              )}
              <UserButton afterSignOutUrl="/" />
            </SignedIn>
            <SignedOut>
              <Link
                to="/sign-in"
                className="text-gray-600 transition-colors hover:text-indigo-600"
              >
                Sign In
              </Link>
              <Link to="/sign-up" className="btn-primary">
                Sign Up
              </Link>
            </SignedOut>
          </div>
        </nav>
      </header>
      <main className="container mx-auto flex-grow px-4 py-8 sm:px-6 lg:px-8">
        <SimpleOutlet />
      </main>
      <footer className="mt-auto bg-gray-100 py-6 text-center">
        <p className="text-sm text-gray-500">
          &copy; {new Date().getFullYear()} Fitness Rewards Platform.
        </p>
      </footer>
    </div>
  );
}

/**
 * Enhanced 404 page with better UX
 */
function NotFoundPage() {
  return (
    <div className="flex min-h-[50vh] items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900">404</h1>
        <p className="mt-2 text-xl text-gray-600">Page Not Found</p>
        <p className="mt-4 text-gray-500">
          The page you're looking for doesn't exist.
        </p>
        <div className="mt-6">
          <Link
            to="/"
            className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-indigo-700"
          >
            Go Home
          </Link>
        </div>
      </div>
    </div>
  );
}

/**
 * Router configuration using createBrowserRouter (React Router v6 best practice)
 */
const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        index: true,
        element: <IndexPage />,
      },
      {
        path: 'sign-in',
        element: <SignInPage />,
      },
      {
        path: 'sign-up',
        element: <SignUpPage />,
      },
      {
        path: 'dashboard',
        element: (
          <AsyncErrorBoundary>
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          </AsyncErrorBoundary>
        ),
      },
      {
        path: 'rewards',
        element: (
          <AsyncErrorBoundary>
            <ProtectedRoute>
              <RewardsPage />
            </ProtectedRoute>
          </AsyncErrorBoundary>
        ),
      },
      {
        path: 'leaderboard',
        element: (
          <AsyncErrorBoundary>
            <ProtectedRoute>
              <LeaderboardPage />
            </ProtectedRoute>
          </AsyncErrorBoundary>
        ),
      },
      {
        path: 'admin',
        element: (
          <ErrorBoundary>
            <ProtectedRoute allowedRoles={['admin', 'staff', 'user']}>
              <AdminPage />
            </ProtectedRoute>
          </ErrorBoundary>
        ),
      },
      {
        path: 'history',
        element: (
          <AsyncErrorBoundary>
            <ProtectedRoute>
              <HistoryLayout />
            </ProtectedRoute>
          </AsyncErrorBoundary>
        ),
        children: [
          {
            path: 'activities',
            element: <ActivityHistoryPage />,
          },
          {
            path: 'milestones',
            element: <MilestonesHistoryPage />,
          },
        ],
      },
      {
        path: '*',
        element: <NotFoundPage />,
      },
    ],
  },
]);

/**
 * Main router component using RouterProvider (React Router v6 data router)
 */
export default function AppRoutes() {
  return <RouterProvider router={router} />;
}
