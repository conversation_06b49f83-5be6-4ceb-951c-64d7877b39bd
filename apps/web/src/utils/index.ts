/**
 * Shared utility functions for the web application.
 */

/**
 * Formats an activity type string for display by converting snake_case to Title Case.
 * @param {string | undefined} type - The activity type string (e.g., "class_attendance")
 * @returns {string} The formatted activity type string (e.g., "Class Attendance")
 * @example
 * formatActivityType("class_attendance") // returns "Class Attendance"
 * formatActivityType(undefined) // returns "Unknown Activity"
 */
export function formatActivityType(type: string | undefined): string {
  if (!type || typeof type !== 'string') {
    return 'Unknown Activity';
  }

  return type
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formats a number as a localized string with appropriate separators.
 * @param {number} value - The number to format
 * @returns {string} The formatted number string
 * @example
 * formatNumber(1234) // returns "1,234"
 */
export function formatNumber(value: number): string {
  return value.toLocaleString();
}

/**
 * Formats a date timestamp for display.
 * @param {number} timestamp - The timestamp to format
 * @param {Intl.DateTimeFormatOptions} [options] - Optional formatting options
 * @returns {string} The formatted date string
 * @example
 * formatDate(Date.now()) // returns "12/25/2023"
 */
export function formatDate(
  timestamp: number,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    ...options,
  };
  
  return new Date(timestamp).toLocaleDateString(undefined, defaultOptions);
}

/**
 * Debounces a function call to prevent excessive executions.
 * @param {Function} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @returns {Function} The debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Validates if a string is a valid email address.
 * @param {string} email - The email string to validate
 * @returns {boolean} True if valid email, false otherwise
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Truncates a string to a specified length with ellipsis.
 * @param {string} str - The string to truncate
 * @param {number} maxLength - The maximum length before truncation
 * @returns {string} The truncated string
 */
export function truncateString(str: string, maxLength: number): string {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - 3) + '...';
}
