import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConvexProviderWithClerk } from 'convex/react-clerk';
import { ClerkProvider, useAuth } from '@clerk/clerk-react';
import { ConvexReactClient } from 'convex/react';
import AppRoutes from './AppRoutes';
import './styles/globals.css';
import { Toaster } from 'react-hot-toast';
import UniversalLoader from './components/UniversalLoader';
import { LoadingProvider, useLoading } from './lib/LoadingContext';

const convexUrl = import.meta.env.VITE_CONVEX_URL!;
const clerkPublishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY!;

const convex = new ConvexReactClient(convexUrl);

/**
 * Main application container component that handles loading states.
 * Shows a universal loader when authentication or data is loading.
 */
function AppContainer(): JSX.Element {
  const { isLoaded } = useAuth();
  const { isLoading: isDataLoading } = useLoading();

  const showLoader = !isLoaded || isDataLoading;

  return (
    <>
      {showLoader && <UniversalLoader />}
      <div style={{ visibility: showLoader ? 'hidden' : 'visible' }}>
        <AppRoutes />
      </div>
    </>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ClerkProvider publishableKey={clerkPublishableKey}>
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <LoadingProvider>
          <AppContainer />
          <Toaster
            position="bottom-center"
            gutter={12}
            containerStyle={{ margin: '8px' }}
            toastOptions={{
              // Default options
              duration: 5000,
              style: {
                background: '#F5F2E8', // Creamy White
                color: '#2D2D2D', // Deep Charcoal
                border: '1px solid #E6D7C3', // Warm Beige
                padding: '16px',
                borderRadius: '12px',
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                fontFamily: 'sans-serif',
              },

              // Success-specific options
              success: {
                duration: 5000,
                iconTheme: {
                  primary: '#D4A574', // Golden Amber
                  secondary: '#F5F2E8', // Creamy White
                },
              },

              // Error-specific options
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#E07A39', // Sunset Orange
                  secondary: '#F5F2E8', // Creamy White
                },
              },
            }}
          />
        </LoadingProvider>
      </ConvexProviderWithClerk>
    </ClerkProvider>
  </React.StrictMode>
);
